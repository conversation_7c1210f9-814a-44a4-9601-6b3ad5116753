#!/usr/bin/env python3
"""
Interactive script to deploy a new Cloud Run service by cloning an existing one.
Usage: python deploy_cloud_run_clone.py <source-service> <new-service-name> <env-value>
Example: python deploy_cloud_run_clone.py alfred alfred-homepro homepro
"""

import argparse
import subprocess
import sys
import tempfile
from typing import Any, Dict

import yaml


class CloudRunCloner:
    def __init__(
        self,
        source_service: str,
        new_service: str,
        env_value: str,
        project_id: str,
        region: str,
        auto_approve: bool = False,
    ):
        self.source_service = source_service
        self.new_service = new_service
        self.env_value = env_value
        self.project_id = project_id
        self.region = region
        self.auto_approve = auto_approve
        self.original_config = None
        self.modified_config = None

    def ask_approval(self, action: str, details: str = "") -> bool:
        """Ask for user approval before proceeding with an action."""
        if self.auto_approve:
            return True

        print(f"\n🔔 Next Step: {action}")
        if details:
            print(f"📋 Details:\n{details}")

        while True:
            response = input("\nProceed? (y/n/details): ").lower().strip()
            if response == "y":
                return True
            elif response == "n":
                print("❌ Operation cancelled by user.")
                return False
            elif response == "details" or response == "d":
                print("\n" + "=" * 60)
                print("Detailed Explanation:")
                print("=" * 60)
                self.show_detailed_help(action)
                print("=" * 60)
            else:
                print(
                    "Please enter 'y' for yes, 'n' for no, or 'details' for more information."
                )

    def show_detailed_help(self, action: str):
        """Show detailed help for each action."""
        help_text = {
            "Export service configuration": """
This step will export the complete configuration of the source Cloud Run service.
The configuration includes:
- Container image and registry location
- Environment variables and secrets
- Resource limits (CPU, memory)
- Scaling configuration (min/max instances)
- Service account and IAM bindings
- Network configuration (VPC connector, ingress settings)
- Cloud SQL connections
- Binary authorization settings

Command that will be executed:
gcloud run services export {service} --format=export --region={region} --project={project}
""",
            "Modify configuration": """
This step will modify the exported configuration to create your new service:

1. Service Name Change:
   - Updates metadata.name from '{source}' to '{new}'

2. Environment Variable Update:
   - Looks for existing ENV variable and updates it to '{env}'
   - If ENV doesn't exist, adds it to the container environment

3. Metadata Cleanup:
   - Removes: uid, resourceVersion, selfLink, generation, creationTimestamp
   - These are auto-generated and must not be included in new deployments

4. Status Removal:
   - Removes the entire 'status' section as it's read-only

The container image, scaling settings, and all other configurations remain identical.
""",
            "Deploy new service": """
This step will create the new Cloud Run service using the modified configuration.

What happens during deployment:
1. Google Cloud validates the configuration
2. Pulls the container image (same as source service)
3. Creates the new service with all settings
4. Allocates resources based on the configuration
5. Sets up networking and load balancing
6. Makes the service available at a new URL

The deployment preserves:
- Same container image version
- All environment variables (with ENV updated)
- Resource allocations
- Scaling behavior
- IAM permissions (service-level)
- Network configuration

Note: Continuous deployment (GitHub/Cloud Build triggers) must be set up separately.
""",
        }

        # Format the help text with actual values
        formatted_help = help_text.get(
            action, "No detailed help available for this action."
        )
        formatted_help = formatted_help.format(
            service=self.source_service,
            source=self.source_service,
            new=self.new_service,
            env=self.env_value,
            region=self.region,
            project=self.project_id,
        )
        print(formatted_help)

    def run_command(
        self, cmd: list, capture_output: bool = True
    ) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        print(f"🔧 Command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=capture_output, text=True)
        if result.returncode != 0:
            print(f"❌ Error: {result.stderr}")
            sys.exit(1)
        return result

    def export_service_config(self) -> Dict[str, Any]:
        """Export the configuration of an existing Cloud Run service."""
        details = f"""
Will export the complete configuration from Cloud Run service: {self.source_service}
Project: {self.project_id}
Region: {self.region}

This includes all settings like image, env vars, scaling, resources, etc.
        """

        if not self.ask_approval("Export service configuration", details):
            sys.exit(0)

        print(f"📋 Exporting configuration from {self.source_service}...")

        cmd = [
            "gcloud",
            "run",
            "services",
            "describe",
            self.source_service,
            "--platform=managed",
            f"--region={self.region}",
            f"--project={self.project_id}",
            "--export",
        ]

        result = self.run_command(cmd)
        self.original_config = yaml.safe_load(result.stdout)
        return self.original_config

    def show_config_diff(self):
        """Show the differences between original and modified configs."""
        print("\n📊 Configuration Changes Summary:")
        print("=" * 60)

        # Service name change
        print(
            f"✏️  Service Name: {self.original_config['metadata']['name']} → {self.modified_config['metadata']['name']}"
        )

        # Environment variable changes
        original_env = {}
        modified_env = {}

        for container in (
            self.original_config.get("spec", {})
            .get("template", {})
            .get("spec", {})
            .get("containers", [])
        ):
            for env in container.get("env", []):
                original_env[env["name"]] = env.get("value", "<not set>")

        for container in (
            self.modified_config.get("spec", {})
            .get("template", {})
            .get("spec", {})
            .get("containers", [])
        ):
            for env in container.get("env", []):
                modified_env[env["name"]] = env.get("value", "<not set>")

        # Show ENV variable change
        if "ENV" in original_env:
            print(
                f"✏️  ENV Variable: {original_env.get('ENV', 'not set')} → {modified_env.get('ENV', 'not set')}"
            )
        else:
            print(f"➕ ENV Variable: <new> → {modified_env.get('ENV', 'not set')}")

        # Show removed metadata
        removed_fields = []
        for field in [
            "uid",
            "resourceVersion",
            "selfLink",
            "generation",
            "creationTimestamp",
        ]:
            if field in self.original_config["metadata"]:
                removed_fields.append(field)
        if removed_fields:
            print(f"🗑️  Removed metadata: {', '.join(removed_fields)}")

        print("=" * 60)

    def modify_service_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Modify the service configuration for the new service."""
        details = f"""
Will make the following changes to the configuration:
1. Change service name: {self.source_service} → {self.new_service}
2. Update ENV variable to: {self.env_value}
3. Remove auto-generated metadata fields
4. Remove status section (read-only)

All other settings (image, resources, scaling, etc.) remain unchanged.
        """

        if not self.ask_approval("Modify configuration", details):
            sys.exit(0)

        print(f"✏️  Modifying configuration for {self.new_service}...")

        # Deep copy to avoid modifying original
        import copy

        config = copy.deepcopy(config)

        # Update service name
        config["metadata"]["name"] = self.new_service

        # Remove metadata that shouldn't be copied
        metadata_to_remove = [
            "uid",
            "resourceVersion",
            "selfLink",
            "generation",
            "creationTimestamp",
        ]
        for field in metadata_to_remove:
            config["metadata"].pop(field, None)

        # Remove status section as it's read-only
        config.pop("status", None)

        # Update ENV variable in the container spec
        if "spec" in config and "template" in config["spec"]:
            containers = config["spec"]["template"]["spec"]["containers"]
            for container in containers:
                if "env" not in container:
                    container["env"] = []

                # Check if ENV variable exists
                env_exists = False
                for env_var in container["env"]:
                    if env_var.get("name") == "ENV":
                        env_var["value"] = self.env_value
                        env_exists = True
                        break

                # If ENV doesn't exist, add it
                if not env_exists:
                    container["env"].append({"name": "ENV", "value": self.env_value})

        self.modified_config = config

        # Show the differences
        self.show_config_diff()

        return config

    def deploy_service(self, config: Dict[str, Any]) -> None:
        """Deploy the new Cloud Run service."""
        # Get the container image for details
        container_image = "unknown"
        if "spec" in config and "template" in config["spec"]:
            containers = config["spec"]["template"]["spec"]["containers"]
            if containers:
                container_image = containers[0].get("image", "unknown")

        details = f"""
Will deploy a new Cloud Run service with:
- Service name: {self.new_service}
- Container image: {container_image}
- ENV variable: {self.env_value}
- Project: {self.project_id}
- Region: {self.region}

This will create a new service. It will NOT affect the source service ({self.source_service}).
        """

        if not self.ask_approval("Deploy new service", details):
            sys.exit(0)

        print(f"🚀 Deploying {self.new_service}...")

        # Write config to temporary file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
            yaml.dump(config, f, default_flow_style=False)
            temp_file = f.name

        try:
            cmd = [
                "gcloud",
                "run",
                "services",
                "replace",
                temp_file,
                "--platform=managed",
                f"--region={self.region}",
                f"--project={self.project_id}",
            ]
            self.run_command(cmd, capture_output=False)
        finally:
            # Clean up temp file
            import os

            os.unlink(temp_file)

    def get_service_url(self, service_name: str) -> str:
        """Get the URL of the deployed service."""
        cmd = [
            "gcloud",
            "run",
            "services",
            "describe",
            service_name,
            "--platform=managed",
            f"--region={self.region}",
            f"--project={self.project_id}",
            "--format=value(status.url)",
        ]

        result = self.run_command(cmd)
        return result.stdout.strip()

    def show_final_summary(self):
        """Show a summary of what was deployed."""
        print("\n" + "=" * 60)
        print("🎉 DEPLOYMENT SUMMARY")
        print("=" * 60)
        print(f"✅ Successfully cloned: {self.source_service} → {self.new_service}")
        print(f"🔧 Environment: ENV={self.env_value}")
        print(f"📍 Project: {self.project_id}")
        print(f"🌍 Region: {self.region}")

        try:
            url = self.get_service_url(self.new_service)
            print(f"🔗 Service URL: {url}")
        except:
            print("⚠️  Could not retrieve service URL")

        print("\n📦 Next Steps:")
        print("1. Test the new service at the URL above")
        print("2. Set up continuous deployment if needed:")
        print(f"   - Go to Cloud Console > Cloud Run > {self.new_service}")
        print("   - Click 'SET UP CONTINUOUS DEPLOYMENT'")
        print("   - Connect to your source repository")
        print("3. Configure custom domain if required")
        print("=" * 60)

    def run(self):
        """Main execution flow."""
        print(f"🚀 Cloud Run Service Cloner")
        print(f"=" * 60)
        print(f"Source Service: {self.source_service}")
        print(f"Target Service: {self.new_service}")
        print(f"Environment: ENV={self.env_value}")
        print(f"Project: {self.project_id}")
        print(f"Region: {self.region}")
        print(
            f"Auto-approve: {'Yes' if self.auto_approve else 'No (interactive mode)'}"
        )
        print(f"=" * 60)

        try:
            # Step 1: Export configuration
            config = self.export_service_config()
            print("✅ Configuration exported successfully\n")

            # Step 2: Modify configuration
            config = self.modify_service_config(config)
            print("✅ Configuration modified successfully\n")

            # Step 3: Deploy service
            self.deploy_service(config)
            print("✅ Service deployed successfully\n")

            # Show summary
            self.show_final_summary()

        except KeyboardInterrupt:
            print("\n\n⚠️  Operation cancelled by user")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Interactively clone a Cloud Run service with a new environment variable",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Interactive mode (asks for approval at each step)
  python deploy_cloud_run_clone.py alfred alfred-homepro homepro

  # Auto-approve all steps
  python deploy_cloud_run_clone.py alfred alfred-homepro homepro --yes

  # Specify custom project and region
  python deploy_cloud_run_clone.py alfred alfred-staging staging --project-id my-project --region us-east1
        """,
    )
    parser.add_argument(
        "source_service", help="Name of the source Cloud Run service to clone"
    )
    parser.add_argument("new_service", help="Name for the new Cloud Run service")
    parser.add_argument("env_value", help="Value for the ENV environment variable")
    parser.add_argument(
        "-y",
        "--yes",
        "--auto-approve",
        action="store_true",
        help="Auto-approve all steps (non-interactive mode)",
    )
    parser.add_argument(
        "--project-id",
        default="llogic-skai",
        help="GCP Project ID (default: llogic-skai)",
    )
    parser.add_argument(
        "--region", default="us-central1", help="GCP Region (default: us-central1)"
    )

    args = parser.parse_args()

    cloner = CloudRunCloner(
        source_service=args.source_service,
        new_service=args.new_service,
        env_value=args.env_value,
        project_id=args.project_id,
        region=args.region,
        auto_approve=args.yes,
    )

    cloner.run()


if __name__ == "__main__":
    main()
