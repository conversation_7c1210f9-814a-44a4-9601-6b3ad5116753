# Log Agent New Architecture - Implementation Plan

## Executive Summary

This document provides a detailed implementation plan for the new log agent architecture that eliminates 98% token duplication, reduces costs by 85%, and provides full task transparency through minimal structured responses.

## Architecture Overview

### New Architecture Diagram

```mermaid
graph TD
    START([🚀 User Query]) --> classifier["🤖 Classifier<br/>200 tokens<br/>~1s"]

    classifier --> route{Needs DB?}

    %% Direct Response Path
    route -->|No| direct["📝 Direct Response<br/>Immediate"]
    direct --> END_DIRECT([✅ Complete<br/>~1s total])

    %% Adaptive Database Path
    route -->|Yes| cache_check["💾 Check Session Cache<br/>Memory context cached?"]

    cache_check --> planning["🧠 Planning + Execute<br/>800 tokens total<br/>Task-specific prompt<br/>~8s"]

    planning --> parse_response["📊 Parse Minimal Response<br/>Extract: action, tasks, tool_query"]

    parse_response --> agent_decision{Agent Action?}

    agent_decision -->|tool| execute_tool["⚡ Execute Tool<br/>MongoDB query<br/>Update task status<br/>~5s"]
    agent_decision -->|answer| final_answer["📝 Generate Answer<br/>300 tokens<br/>Mark tasks complete<br/>~3s"]
    agent_decision -->|clarify| clarify["❓ Request Clarification<br/>Update task status"]

    execute_tool --> continue_agent["🤖 Continue Agent<br/>400 tokens<br/>Cached context<br/>~3s"]

    continue_agent --> agent_decision2{Agent Action?}
    agent_decision2 -->|tool| execute_tool
    agent_decision2 -->|answer| final_answer

    final_answer --> END_DB([✅ Complete<br/>~15s total])
    clarify --> END_CLARIFY([❓ Awaiting User])

    %% Real-time Task Updates (parallel)
    planning -.-> task_ui["📋 Task UI Updates<br/>Real-time status<br/>No extra LLM calls"]
    continue_agent -.-> task_ui
    execute_tool -.-> task_ui
    final_answer -.-> task_ui

    %% Session Cache (persistent)
    cache_check -.-> session_cache["💾 Session Cache<br/>Memory context<br/>Prompt templates<br/>Previous results"]
    planning -.-> session_cache
    continue_agent -.-> session_cache

    %% Styling
    classDef startEnd fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000000
    classDef conversational fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000000
    classDef adaptive fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000000
    classDef minimal fill:#e8eaf6,stroke:#3f51b5,stroke-width:3px,color:#000000
    classDef execution fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000000
    classDef caching fill:#e0f2f1,stroke:#00695c,stroke-width:2px,stroke-dasharray: 3 3,color:#000000
    classDef ui fill:#fff8e1,stroke:#ff8f00,stroke-width:2px,stroke-dasharray: 5 5,color:#000000
    classDef decision fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000000

    class START,END_DIRECT,END_DB,END_CLARIFY startEnd
    class direct conversational
    class planning,continue_agent adaptive
    class parse_response,final_answer minimal
    class execute_tool execution
    class cache_check,session_cache caching
    class task_ui ui
    class route,agent_decision,agent_decision2 decision
```

### Current vs. New Architecture Comparison

#### Current Problems (From Debug Analysis)
- **98% token duplication**: 40,730 of 41,522 tokens are repeated content
- **Wrong prompts for tasks**: Planning gets MongoDB syntax instructions instead of strategic guidance
- **6 LLM calls**: Rigid plan-execute pattern with redundant steps
- **No user visibility**: 75-second black box execution
- **Cost**: $0.026 for simple "give me app ids" query

#### New Architecture Benefits
- **Minimal structured responses**: Only essential data, no verbose descriptions
- **Task-specific prompts**: Right prompt for each type of work
- **Session caching**: Eliminate context repetition within sessions
- **Adaptive execution**: Agent decides when ready to answer
- **Full transparency**: Real-time task updates without extra calls
- **Target**: 3-4 calls, ~$0.004 cost, 15-20s execution

### Token Flow Comparison

#### Current Flow (98% Duplication)
```
Call 1: Classification     →  263 tokens
Call 2: Planning          → 9,777 tokens (4k system + 4k memory + 1k unique)
Call 3: Execute Step 1    → 5,434 tokens (4k system + 4k memory + 1k unique)
Call 4: Execute Step 2    → 7,258 tokens (4k system + 4k memory + 1k unique)
Call 5: Execute Step 3    → 9,787 tokens (4k system + 4k memory + 1k unique)
Call 6: Final Answer      → 6,696 tokens (4k system + 4k memory + 1k unique)
                          ─────────────────────────────────────────────────
Total:                     39,215 tokens (40k duplication + 5k unique)
```

#### New Flow (95% Efficiency)
```
Call 1: Classification     →   200 tokens (minimal prompt)
Call 2: Planning+Execute   →   800 tokens (cached context + minimal response)
Call 3: Continue          →   400 tokens (context reference + minimal response)
Call 4: Final Answer      →   300 tokens (minimal formatting prompt)
                          ─────────────────────────────────────────────────
Total:                      1,700 tokens (95% unique content)
```

## Implementation Plan

### Phase 1: Minimal Structured Response Schema (Day 1)

#### Core Response Schema
```python
from enum import Enum
from dataclasses import dataclass
from typing import List, Optional, Dict, Any

class AgentAction(Enum):
    TOOL = "tool"           # Execute database query
    ANSWER = "answer"       # Provide final response
    CLARIFY = "clarify"     # Need user clarification

class TaskStatus(Enum):
    PENDING = "pending"
    ACTIVE = "active"
    DONE = "done"
    FAILED = "failed"

@dataclass
class Task:
    id: str
    desc: str  # Short description, max 50 chars
    status: TaskStatus
    result: Optional[str] = None  # Brief result if done

@dataclass
class AgentResponse:
    action: AgentAction
    tasks: List[Task]
    tool_query: Optional[Dict] = None  # Only if action=TOOL
    answer: Optional[str] = None       # Only if action=ANSWER
    reason: Optional[str] = None       # Brief reasoning, max 100 chars

# JSON Schema for LLM structured output
MINIMAL_AGENT_SCHEMA = {
    "type": "object",
    "properties": {
        "action": {"type": "string", "enum": ["tool", "answer", "clarify"]},
        "tasks": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "desc": {"type": "string", "maxLength": 50},
                    "status": {"type": "string", "enum": ["pending", "active", "done", "failed"]},
                    "result": {"type": "string", "maxLength": 100}
                },
                "required": ["id", "desc", "status"]
            }
        },
        "tool_query": {
            "type": "object",
            "properties": {
                "collection": {"type": "string"},
                "pipeline": {"type": "array"}
            }
        },
        "answer": {"type": "string"},
        "reason": {"type": "string", "maxLength": 100}
    },
    "required": ["action", "tasks"]
}
```

#### Key Design Principles
- **Minimal tokens**: Short field names (`desc` vs `description`), character limits
- **Essential only**: No verbose explanations or reasoning unless critical
- **Structured data**: Machine-readable format for UI updates
- **Conditional fields**: Only include `tool_query` if action=TOOL, etc.

### Phase 2: Task-Specific Prompts (Day 2)

#### Prompt Templates
```python
class PromptTemplates:

    # Classification: 200 tokens
    CLASSIFICATION = """
    Classify query type:
    Query: "{query}"

    Return: {{"needs_db": true/false, "reason": "brief explanation"}}

    Examples:
    - "hello" → {{"needs_db": false, "reason": "greeting"}}
    - "give me app ids" → {{"needs_db": true, "reason": "data request"}}
    """

    # Initial Planning: 300 tokens
    INITIAL_PLANNING = """
    Create 2-3 tasks for: "{query}"
    Memory: {memory_summary}

    Return structured response with:
    - action: "tool" (start with first task)
    - tasks: [list of 2-3 tasks with short descriptions]
    - tool_query: MongoDB query for first task
    - reason: brief explanation

    Keep task descriptions under 50 chars.
    """

    # Execution Continue: 150 tokens
    EXECUTION_CONTINUE = """
    Previous results: {tool_results}
    Current tasks: {current_tasks}
    Original query: "{query}"

    Next action? Return structured response.
    """

    # Final Answer: 100 tokens
    FINAL_ANSWER = """
    Generate final answer for: "{query}"
    Data: {all_results}

    Return: {{"action": "answer", "tasks": [mark all done], "answer": "response"}}
    """

def get_prompt(stage: str, **kwargs) -> str:
    """Get minimal prompt for specific stage"""
    template = getattr(PromptTemplates, stage.upper())
    return template.format(**kwargs)
```

#### Memory Context Optimization
```python
class MemoryManager:

    def get_context(self, query: str, stage: str) -> str:
        """Return minimal relevant memory based on stage"""

        if stage == "classification":
            return ""  # No memory needed

        elif stage == "initial_planning":
            # Only essential field mappings and patterns
            relevant = self.find_relevant_fields(query)
            return self.format_minimal_memory(relevant, max_chars=500)

        elif stage == "execution":
            # Only if query has ambiguous field references
            if self.has_field_ambiguity(query):
                return self.get_field_mappings(max_chars=200)
            return ""

        elif stage == "final_answer":
            return ""  # No memory needed for formatting

    def format_minimal_memory(self, items: List, max_chars: int) -> str:
        """Format memory context with strict character limit"""
        formatted = []
        char_count = 0

        for item in items:
            summary = f"{item.field}: {item.description[:50]}"
            if char_count + len(summary) > max_chars:
                break
            formatted.append(summary)
            char_count += len(summary)

        return "\n".join(formatted)
```

### Phase 3: Adaptive Agent Implementation (Days 3-4)

#### Core Agent Loop
```python
class AdaptiveLogAgent:

    def __init__(self):
        self.prompt_templates = PromptTemplates()
        self.memory_manager = MemoryManager()
        self.session_cache = {}  # Cache context per session

    async def process_query(self, query: str, session_id: str) -> Dict:
        """Main entry point for query processing"""

        # Step 1: Classification (minimal prompt)
        classification = await self.classify_query(query)

        if not classification["needs_db"]:
            return {"answer": self.get_direct_response(query)}

        # Step 2: Initial planning with first execution
        response = await self.initial_planning_and_execute(query, session_id)

        # Step 3: Continue until ready to answer
        while response["action"] != "answer":
            response = await self.continue_execution(response, query, session_id)

        return response

    async def classify_query(self, query: str) -> Dict:
        """Lightweight classification - 200 tokens max"""
        prompt = self.prompt_templates.get_prompt("classification", query=query)

        return await self.llm_call(
            prompt=prompt,
            max_tokens=50,  # Minimal response
            schema={"type": "object", "properties": {
                "needs_db": {"type": "boolean"},
                "reason": {"type": "string", "maxLength": 50}
            }}
        )

    async def initial_planning_and_execute(self, query: str, session_id: str) -> AgentResponse:
        """Combined planning and first execution - 800 tokens max"""

        # Get minimal memory context
        memory_context = self.memory_manager.get_context(query, "initial_planning")

        prompt = self.prompt_templates.get_prompt(
            "initial_planning",
            query=query,
            memory_summary=memory_context
        )

        # Cache the memory context for this session
        self.session_cache[session_id] = {
            "memory_context": memory_context,
            "query": query
        }

        response = await self.llm_call(
            prompt=prompt,
            max_tokens=200,  # Structured response only
            schema=MINIMAL_AGENT_SCHEMA
        )

        # Execute the tool if specified
        if response["action"] == "tool" and response.get("tool_query"):
            tool_result = await self.execute_tool(response["tool_query"])

            # Update task status
            for task in response["tasks"]:
                if task["status"] == "active":
                    task["status"] = "done"
                    task["result"] = f"Found {len(tool_result)} items"
                    break

        return response

    async def continue_execution(self, previous_response: Dict, query: str, session_id: str) -> Dict:
        """Continue execution based on current state - 400 tokens max"""

        # Use cached context
        cached = self.session_cache.get(session_id, {})

        prompt = self.prompt_templates.get_prompt(
            "execution_continue",
            tool_results=previous_response.get("tool_results", ""),
            current_tasks=previous_response["tasks"],
            query=query
        )

        response = await self.llm_call(
            prompt=prompt,
            max_tokens=150,  # Minimal structured response
            schema=MINIMAL_AGENT_SCHEMA
        )

        # Execute tool if needed
        if response["action"] == "tool" and response.get("tool_query"):
            tool_result = await self.execute_tool(response["tool_query"])
            response["tool_results"] = tool_result

            # Update task status
            for task in response["tasks"]:
                if task["status"] == "active":
                    task["status"] = "done"
                    task["result"] = f"Completed"
                    break

        return response

    async def llm_call(self, prompt: str, max_tokens: int, schema: Dict) -> Dict:
        """Make LLM call with strict token limits"""

        # Enforce token limits
        if len(prompt.split()) > max_tokens * 0.75:  # Rough token estimation
            raise ValueError(f"Prompt too long: {len(prompt)} chars")

        # Make actual LLM call with structured output
        response = await self.llm_client.generate(
            prompt=prompt,
            max_tokens=max_tokens,
            response_format={"type": "json_object", "schema": schema}
        )

        return response
```

### Phase 4: UI Integration (Day 5)

#### Real-time Task Display
```python
class TaskTracker:

    def __init__(self):
        self.active_sessions = {}

    async def update_tasks(self, session_id: str, tasks: List[Task]):
        """Update task display in real-time"""

        # Convert to UI format
        ui_tasks = []
        for task in tasks:
            ui_tasks.append({
                "id": task.id,
                "description": task.desc,
                "status": self.get_status_icon(task.status),
                "result": task.result or ""
            })

        # Send to UI via websocket/SSE
        await self.send_to_ui(session_id, {
            "type": "task_update",
            "tasks": ui_tasks
        })

    def get_status_icon(self, status: TaskStatus) -> str:
        """Convert status to UI icon"""
        icons = {
            TaskStatus.PENDING: "⏳",
            TaskStatus.ACTIVE: "🔄",
            TaskStatus.DONE: "✅",
            TaskStatus.FAILED: "❌"
        }
        return icons.get(status, "❓")
```

## Expected Performance Improvements

### Token Usage Comparison
| Component | Current | New | Reduction |
|-----------|---------|-----|-----------|
| **Classification** | 263 tokens | 200 tokens | 24% |
| **Planning** | 9,777 tokens | 800 tokens | 92% |
| **Execution 1** | 5,434 tokens | 400 tokens | 93% |
| **Execution 2** | 7,258 tokens | 400 tokens | 94% |
| **Execution 3** | 9,787 tokens | 400 tokens | 96% |
| **Final Answer** | 6,696 tokens | 300 tokens | 96% |
| **TOTAL** | **39,215 tokens** | **2,500 tokens** | **94%** |

### Cost & Performance
- **Cost**: $0.026 → $0.004 (85% reduction)
- **Time**: 75s → 15s (80% reduction)
- **Calls**: 6 → 3-4 (33% reduction)
- **User Experience**: Black box → Full transparency

## Implementation Timeline

### Week 1: Core Implementation
- **Day 1**: Minimal structured response schema
- **Day 2**: Task-specific prompt templates
- **Day 3-4**: Adaptive agent loop
- **Day 5**: UI integration

### Week 2: Testing & Optimization
- **Day 6-7**: Unit tests and integration tests
- **Day 8-9**: Performance testing with real queries
- **Day 10**: Documentation and deployment prep

### Week 3: Deployment
- **Day 11-12**: Staged rollout (10% traffic)
- **Day 13-14**: Monitor and adjust
- **Day 15**: Full deployment

## Critical Implementation Details

### Structured Response Optimization

#### Token Minimization Strategies
```python
# BAD: Verbose structured response (200+ tokens)
{
    "agent_decision": "use_tool",
    "reasoning": "I need to query the database to find the most recent application IDs as requested by the user",
    "current_step_description": "Query database for recent application IDs with summaries",
    "tool_call_details": {
        "database_collection": "logs",
        "query_type": "aggregation",
        "pipeline_description": "Sort by timestamp, group by app ID, limit to 5"
    }
}

# GOOD: Minimal structured response (50 tokens)
{
    "action": "tool",
    "tasks": [
        {"id": "1", "desc": "Get recent app IDs", "status": "active"},
        {"id": "2", "desc": "Format results", "status": "pending"}
    ],
    "tool_query": {"collection": "logs", "pipeline": [...]}
}
```

#### Field Name Optimization
```python
# Use shortest meaningful names
FIELD_MAPPINGS = {
    "description": "desc",           # 50% shorter
    "status": "status",              # Keep short names
    "tool_call": "tool_query",       # More specific
    "reasoning": "reason",           # 44% shorter
    "application_id": "app_id",      # 60% shorter
}
```

### Prompt Engineering Best Practices

#### Template Validation
```python
class PromptValidator:

    MAX_TOKENS = {
        "classification": 200,
        "initial_planning": 800,
        "execution_continue": 400,
        "final_answer": 300
    }

    def validate_prompt(self, stage: str, prompt: str) -> bool:
        """Ensure prompts stay within token limits"""
        estimated_tokens = len(prompt.split()) * 1.3  # Conservative estimate
        max_allowed = self.MAX_TOKENS[stage]

        if estimated_tokens > max_allowed:
            raise ValueError(f"{stage} prompt too long: {estimated_tokens} > {max_allowed}")

        return True

    def optimize_prompt(self, prompt: str) -> str:
        """Remove unnecessary words while preserving meaning"""
        # Remove filler words
        filler_words = ["please", "kindly", "essentially", "basically", "actually"]
        for word in filler_words:
            prompt = prompt.replace(f" {word} ", " ")

        # Compress common phrases
        replacements = {
            "in order to": "to",
            "due to the fact that": "because",
            "at this point in time": "now",
            "for the purpose of": "to"
        }

        for old, new in replacements.items():
            prompt = prompt.replace(old, new)

        return prompt.strip()
```

#### Context Caching Strategy
```python
class SessionContextCache:

    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour

    def cache_context(self, session_id: str, context_type: str, content: str):
        """Cache context to avoid repetition"""
        key = f"{session_id}:{context_type}"
        self.cache[key] = {
            "content": content,
            "timestamp": time.time(),
            "hash": hashlib.md5(content.encode()).hexdigest()
        }

    def get_cached_context(self, session_id: str, context_type: str) -> Optional[str]:
        """Retrieve cached context if still valid"""
        key = f"{session_id}:{context_type}"
        cached = self.cache.get(key)

        if cached and (time.time() - cached["timestamp"]) < self.cache_ttl:
            return cached["content"]

        return None

    def build_context_reference(self, session_id: str, context_type: str) -> str:
        """Build minimal reference to cached context"""
        cached = self.cache.get(f"{session_id}:{context_type}")
        if cached:
            return f"[Use cached {context_type} context: {cached['hash'][:8]}]"
        return ""
```

### Error Handling & Fallbacks

#### Graceful Degradation
```python
class AgentErrorHandler:

    async def handle_structured_response_error(self, response: str, schema: Dict) -> Dict:
        """Handle malformed structured responses"""
        try:
            # Try to parse as JSON
            parsed = json.loads(response)
            return self.validate_and_fix_schema(parsed, schema)
        except json.JSONDecodeError:
            # Fallback to text parsing
            return self.extract_from_text(response, schema)

    def validate_and_fix_schema(self, data: Dict, schema: Dict) -> Dict:
        """Fix common schema violations"""
        required_fields = schema.get("required", [])

        # Add missing required fields with defaults
        for field in required_fields:
            if field not in data:
                data[field] = self.get_default_value(field, schema)

        # Truncate fields that exceed maxLength
        for field, value in data.items():
            if isinstance(value, str):
                max_length = self.get_max_length(field, schema)
                if max_length and len(value) > max_length:
                    data[field] = value[:max_length-3] + "..."

        return data

    def get_default_value(self, field: str, schema: Dict) -> Any:
        """Get sensible defaults for missing fields"""
        defaults = {
            "action": "clarify",
            "tasks": [],
            "reason": "Processing..."
        }
        return defaults.get(field, "")
```

## Success Metrics

### Performance Targets
- **Token usage**: <3,000 tokens per query (vs 41,522 current)
- **Cost**: <$0.005 per query (vs $0.026 current)
- **Execution time**: <20 seconds (vs 75s current)
- **User satisfaction**: >90% positive feedback on transparency

### Monitoring Dashboard
```python
class PerformanceMonitor:

    def track_query_metrics(self, session_id: str, metrics: Dict):
        """Track key performance indicators"""
        self.metrics_db.insert({
            "session_id": session_id,
            "timestamp": time.time(),
            "total_tokens": metrics["total_tokens"],
            "total_cost": metrics["total_cost"],
            "execution_time": metrics["execution_time"],
            "llm_calls": metrics["llm_calls"],
            "user_satisfaction": metrics.get("user_rating"),
            "query_type": metrics["query_type"]
        })

    def generate_efficiency_report(self) -> Dict:
        """Generate efficiency comparison report"""
        recent_queries = self.get_recent_queries(days=7)

        return {
            "avg_tokens_per_query": np.mean([q["total_tokens"] for q in recent_queries]),
            "avg_cost_per_query": np.mean([q["total_cost"] for q in recent_queries]),
            "avg_execution_time": np.mean([q["execution_time"] for q in recent_queries]),
            "token_efficiency": self.calculate_efficiency_ratio(recent_queries),
            "cost_savings_vs_old": self.calculate_savings(recent_queries)
        }
```

### A/B Testing Framework
```python
class ABTestManager:

    def should_use_new_architecture(self, session_id: str) -> bool:
        """Determine which architecture to use for this session"""
        # Start with 10% traffic to new architecture
        hash_value = int(hashlib.md5(session_id.encode()).hexdigest(), 16)
        return (hash_value % 100) < self.rollout_percentage

    def track_comparison_metrics(self, session_id: str, architecture: str, metrics: Dict):
        """Track metrics for A/B comparison"""
        self.ab_metrics_db.insert({
            "session_id": session_id,
            "architecture": architecture,  # "old" or "new"
            "metrics": metrics,
            "timestamp": time.time()
        })
```

---

*Implementation plan for new log agent architecture*
*Target: 94% token reduction, 85% cost savings, full transparency*
*Focus: Minimal structured responses, task-specific prompts, adaptive execution*
