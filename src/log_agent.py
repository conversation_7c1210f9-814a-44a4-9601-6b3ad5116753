#!/usr/bin/env python3
"""
LangGraph-based Log Intelligence Agent
====================================

This is the new implementation using LangGraph's Plan-and-Execute pattern
to replace the custom task management system.

Key improvements:
- Uses LangGraph Plan-and-Execute workflow
- Eliminates custom task management tools
- Single @tool decorator for MongoDB queries
- Built-in state management and progress tracking
- Significantly reduced LLM calls
"""

import argparse
import logging
import operator
import time
import uuid
from datetime import datetime
from typing import Annotated, Any, Dict, List

from google.genai import types
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, ToolMessage
from langgraph.graph import END, START, StateGraph
from langgraph.prebuilt import ToolNode

from src.client_managers import get_genai_manager
from src.database_manager import DatabaseManager
from src.env_config import EnvConfig
from src.log_config import setup_logging
from src.logging_utils import LoggingUtils
from src.memory import MemoryManager
from src.mongodb_log_handler import MongoDBLogHandler
from src.prompt_manager import PromptManager
from src.retry_utils import call_google_genai_with_retry


class PlanExecuteState(Dict[str, Any]):
    """
    State for Plan-and-Execute workflow.

    This replaces the custom TaskManager with LangGraph's built-in state management.
    """

    # User input
    input: Annotated[str, lambda x, y: y]

    # Messages for agent communication (same as POC)
    messages: Annotated[List[BaseMessage], operator.add]

    # Generated plan (list of steps)
    plan: Annotated[List[str], lambda x, y: y]

    # Executed steps with results
    past_steps: Annotated[List[tuple], operator.add]

    # Final response
    response: Annotated[str, lambda x, y: y]

    # Session tracking
    session_id: Annotated[str, lambda x, y: y]
    run_id: Annotated[str, lambda x, y: y]
    persona: Annotated[str, lambda x, y: y]

    # Current step being executed
    current_step: Annotated[str, lambda x, y: y]

    # Step execution counter to prevent infinite loops
    step_execution_count: Annotated[int, lambda x, y: y]

    # MongoDB results for analysis
    mongodb_results: Annotated[Dict[str, Any], lambda x, y: {**x, **y}]

    # Chat history context from previous messages
    chat_history_context: Annotated[str, lambda x, y: y]

    # Memory context from consultation
    memory_context: Annotated[Dict[str, Any], lambda x, y: {**x, **y}]

    # Memory IDs used in response (for feedback)
    memory_ids_used: Annotated[List[str], operator.add]


class LogIntelligenceAgentLangGraph:
    """
    LangGraph-based Log Intelligence Agent using Plan-and-Execute pattern.

    This replaces the old agent's custom task management with LangGraph's
    built-in workflow patterns for better efficiency and maintainability.
    """

    def __init__(self, config: EnvConfig = None, env_file: str = None):
        """Initialize the LangGraph-based agent."""
        if config is not None:
            self.config = config
        else:
            self.config = EnvConfig.load(env_file)

        # Initialize database manager
        self.database_manager = DatabaseManager(self.config)

        # Initialize prompt manager
        self.prompt_manager = PromptManager()

        # Initialize memory manager
        self.memory_manager = MemoryManager(self.config)

        # Initialize Google GenAI client using the client manager
        self.genai_manager = get_genai_manager(self.config)
        self.client = self.genai_manager.get_client()

        # Create the workflow
        self.workflow = self._create_workflow()

        # Session task tracking for UI compatibility
        self.session_tasks = {}

        # Current session context for tool execution
        self._current_session_id = None

        # Cost tracking per run_id
        self.run_costs = {}

    def _format_memory_context(self, memory_context: Dict[str, Any]) -> str:
        """Format memory context into a readable prompt section."""
        if not memory_context:
            return ""

        sections = []

        # Format domain workflows
        domain_workflows = memory_context.get("domain_workflows", [])
        if domain_workflows:
            sections.append("### Analytical Workflows:")
            for workflow in domain_workflows:
                canonical_question = workflow.get("canonical_question", "")
                answer_plan = workflow.get("answer_plan", {})

                # Format the answer plan
                if answer_plan:
                    steps = answer_plan.get("steps", [])
                    strategy = answer_plan.get("strategy", "")

                    plan_text = f"**Question Pattern**: {canonical_question}\n"
                    if steps:
                        plan_text += "**Analysis Steps**:\n"
                        for i, step in enumerate(steps, 1):
                            plan_text += f"{i}. {step}\n"
                    if strategy:
                        plan_text += f"**Strategy**: {strategy}\n"

                    sections.append(plan_text)

        # Format field knowledge
        field_knowledge = memory_context.get("field_knowledge", [])
        if field_knowledge:
            sections.append("### Field Information:")
            for knowledge in field_knowledge:
                title = knowledge.get("title", "")
                body = knowledge.get("body", "")
                field_type = knowledge.get("field_type", "")
                aliases = knowledge.get("aliases", [])

                knowledge_text = f"**{title}** ({field_type}): {body}"
                if aliases:
                    knowledge_text += f" [Aliases: {', '.join(aliases)}]"

                sections.append(knowledge_text)

        # Format translation mappings
        translation_mappings = memory_context.get("translation_mappings", [])
        if translation_mappings:
            sections.append("### Business Terminology:")
            for mapping in translation_mappings:
                title = mapping.get("title", "")
                body = mapping.get("body", "")
                aliases = mapping.get("aliases", [])

                mapping_text = f"**{title}**: {body}"
                if aliases:
                    mapping_text += f" [Also: {', '.join(aliases)}]"

                sections.append(mapping_text)

        # Format agent guidance
        agent_guidance = memory_context.get("agent_guidance", [])
        if agent_guidance:
            sections.append("### Agent Guidance:")
            for guidance in agent_guidance:
                title = guidance.get("title", "")
                body = guidance.get("body", "")

                guidance_text = f"**{title}**: {body}"
                sections.append(guidance_text)

        if sections:
            formatted_context = (
                "\n## Relevant Knowledge for This Query:\n\n"
                + "\n\n".join(sections)
                + "\n"
            )

            # Log the full constructed memory context
            logging.info(
                "📝 Memory context formatted for prompt",
                extra={
                    "details": {
                        "context_sections_count": len(sections),
                        "context_length": len(formatted_context),
                        "full_context": formatted_context,
                        "has_domain_workflows": any(
                            "Analytical Workflows" in s for s in sections
                        ),
                        "has_field_knowledge": any(
                            "Field Information" in s for s in sections
                        ),
                        "has_translation_mappings": any(
                            "Business Terminology" in s for s in sections
                        ),
                        "has_agent_guidance": any(
                            "Agent Guidance" in s for s in sections
                        ),
                    }
                },
            )

            return formatted_context

        return ""

    def _log_api_usage_and_cost(
        self, response, call_type: str, session_id: str = None, run_id: str = None
    ):
        """Log exact API usage and estimated cost per call."""
        usage_metadata = getattr(response, "usage_metadata", None)
        if not usage_metadata:
            logging.warning(
                f"No usage metadata available for {call_type} call. Response type: {type(response)}"
            )
            return

        # Extract token counts
        prompt_tokens = getattr(usage_metadata, "prompt_token_count", 0) or 0
        response_tokens = getattr(usage_metadata, "candidates_token_count", 0) or 0
        total_tokens = getattr(usage_metadata, "total_token_count", 0) or 0
        cached_tokens = getattr(usage_metadata, "cached_content_token_count", 0) or 0

        # Estimate cost (Gemini 2.5 Pro pricing as of 2025)
        # Input: $2.50 per 1M tokens, Output: $15.00 per 1M tokens
        input_cost = (prompt_tokens / 1_000_000) * 2.50
        output_cost = (response_tokens / 1_000_000) * 15.00
        total_cost = input_cost + output_cost

        # Track cost per run_id
        if run_id:
            if run_id not in self.run_costs:
                self.run_costs[run_id] = {
                    "total_cost": 0.0,
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "api_calls": 0,
                    "calls": [],
                }

            self.run_costs[run_id]["total_cost"] += total_cost
            self.run_costs[run_id]["total_input_tokens"] += prompt_tokens
            self.run_costs[run_id]["total_output_tokens"] += response_tokens
            self.run_costs[run_id]["api_calls"] += 1

            call_record = {
                "call_type": call_type,
                "prompt_tokens": prompt_tokens,
                "response_tokens": response_tokens,
                "total_tokens": total_tokens,
                "cached_tokens": cached_tokens,
                "estimated_cost": total_cost,
                "timestamp": time.time(),
            }
            self.run_costs[run_id]["calls"].append(call_record)

        # Log detailed usage and cost information
        logging.info(
            f"💰 API Usage & Cost - {call_type}",
            extra={
                "details": {
                    "call_type": call_type,
                    "session_id": session_id,
                    "run_id": run_id,
                    "prompt_tokens": prompt_tokens,
                    "response_tokens": response_tokens,
                    "total_tokens": total_tokens,
                    "cached_content_tokens": cached_tokens,
                    "estimated_input_cost_usd": round(input_cost, 6),
                    "estimated_output_cost_usd": round(output_cost, 6),
                    "estimated_total_cost_usd": round(total_cost, 6),
                    "run_total_cost_usd": (
                        round(self.run_costs.get(run_id, {}).get("total_cost", 0), 6)
                        if run_id
                        else None
                    ),
                    "run_total_calls": (
                        self.run_costs.get(run_id, {}).get("api_calls", 0)
                        if run_id
                        else None
                    ),
                }
            },
        )

    def get_run_cost_summary(self, run_id: str) -> dict:
        """Get cost summary for a specific run."""
        if run_id not in self.run_costs:
            return {
                "run_id": run_id,
                "total_cost": 0.0,
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "api_calls": 0,
                "calls": [],
            }
        return {"run_id": run_id, **self.run_costs[run_id]}

    def _estimate_input_tokens(self, system_prompt: str, contents: list) -> int:
        """Estimate total input tokens for an API call."""
        # Estimate system prompt tokens
        system_tokens = len(system_prompt) // 4

        # Estimate content tokens
        content_tokens = 0
        for content in contents:
            if hasattr(content, "parts") and content.parts:
                for part in content.parts:
                    if hasattr(part, "text") and part.text:
                        content_tokens += len(part.text) // 4

        return system_tokens + content_tokens

    def _check_input_size_before_api_call(
        self, system_prompt: str, contents: list, call_type: str = "api_call"
    ) -> dict:
        """Check input size before API call and return analysis."""
        estimated_input_tokens = self._estimate_input_tokens(system_prompt, contents)

        # Gemini 1.5 Pro has a 1M token input limit
        input_limit = 1048576  # 1M tokens
        warning_threshold = 800000  # 800k tokens - when to warn
        critical_threshold = input_limit * 0.95  # 95% of limit

        needs_warning = estimated_input_tokens > warning_threshold
        will_likely_fail = estimated_input_tokens > critical_threshold

        analysis = {
            "estimated_input_tokens": estimated_input_tokens,
            "input_limit": input_limit,
            "warning_threshold": warning_threshold,
            "critical_threshold": critical_threshold,
            "needs_warning": needs_warning,
            "will_likely_fail": will_likely_fail,
            "call_type": call_type,
            "system_prompt_tokens": len(system_prompt) // 4,
            "content_tokens": estimated_input_tokens - (len(system_prompt) // 4),
        }

        if will_likely_fail:
            logging.error(
                f"🚨 CRITICAL: {call_type} input approaching context limit - likely to fail",
                extra={"details": analysis},
            )
        elif needs_warning:
            logging.warning(
                f"⚠️ Large input detected for {call_type} - may approach context limits",
                extra={"details": analysis},
            )
        else:
            logging.info(
                f"✅ {call_type} input size within safe limits",
                extra={"details": analysis},
            )

        return analysis

    def _load_session_tasks_from_db(self, session_id: str):
        """Load session tasks from MongoDB into memory cache (write-through cache pattern)."""
        try:
            db_data = self.database_manager.get_all_session_run_tasks(session_id)
            if db_data:
                # Convert to the in-memory format expected by the existing code
                self.session_tasks[session_id] = {
                    "runs": {},
                    "current_run_id": db_data.get("current_run_id"),
                    "created_at": None,
                }

                # Convert each run's data to the expected format
                for run_id, run_data in db_data.get("runs", {}).items():
                    metadata = run_data.get("metadata", {})
                    self.session_tasks[session_id]["runs"][run_id] = {
                        "tasks": run_data.get("tasks", []),
                        "created_at": metadata.get("created_at"),
                        "status": metadata.get("status"),
                        "question": metadata.get("question", ""),
                        "last_updated": metadata.get("tasks_updated_at"),
                    }

                logging.info(
                    f"Loaded session tasks for {session_id} from database into cache"
                )
                return True

            return False

        except Exception as e:
            logging.exception(
                f"Error loading session tasks from database for {session_id}: {e}"
            )
            return False

    def get_session_tasks(self, session_id: str, run_id: str = None) -> dict:
        """Get current task status for a session, optionally for a specific run."""
        logging.info(
            f"get_session_tasks called for session_id={session_id}, run_id={run_id}"
        )

        # Write-through cache: Check memory first, then database
        if session_id not in self.session_tasks:
            logging.info(f"Session {session_id} not in cache, loading from database")
            self._load_session_tasks_from_db(session_id)

        if session_id not in self.session_tasks:
            logging.info(f"Session {session_id} not found in cache or database")
            return {
                "session_id": session_id,
                "run_id": run_id,
                "tasks": [],
                "summary": {
                    "total_tasks": 0,
                    "by_status": {},
                    "pending_tasks": [],
                    "in_progress_tasks": [],
                    "completed_tasks": [],
                    "failed_tasks": [],
                },
                "message": "No active plan for this session",
                "agent_type": "langgraph",
            }

        session_data = self.session_tasks[session_id]
        logging.info(
            f"Session {session_id} data: current_run_id={session_data.get('current_run_id')}, available_runs={list(session_data.get('runs', {}).keys())}"
        )

        # If no run_id specified, use the current active run
        if run_id is None:
            run_id = session_data.get("current_run_id")

        # If still no run_id, try to use the most recent run
        runs = session_data.get("runs", {})
        if not run_id and runs:
            # Get the most recent run (assuming runs are created in order)
            run_id = max(runs.keys())
            logging.info(
                f"No current_run_id found for session {session_id}, using most recent run: {run_id}"
            )

        # If still no run_id or run doesn't exist, return empty
        if not run_id or run_id not in runs:
            available_runs = list(runs.keys()) if runs else []
            return {
                "session_id": session_id,
                "run_id": run_id,
                "tasks": [],
                "summary": {
                    "total_tasks": 0,
                    "by_status": {},
                    "pending_tasks": [],
                    "in_progress_tasks": [],
                    "completed_tasks": [],
                    "failed_tasks": [],
                },
                "message": f"No active plan for this session. Available runs: {available_runs}",
                "agent_type": "langgraph",
                "available_runs": available_runs,
                "current_run_id": session_data.get("current_run_id"),
            }

        run_data = session_data["runs"][run_id]
        return {
            "session_id": session_id,
            "run_id": run_id,
            "tasks": run_data["tasks"],
            "summary": self._calculate_task_summary(run_data["tasks"]),
            "message": f"Plan with {len(run_data['tasks'])} steps",
            "agent_type": "langgraph",
            "plan_created_at": run_data.get("created_at"),
            "last_updated": run_data.get("last_updated"),
            "question": run_data.get("question", ""),
            "status": run_data.get("status", "active"),
        }

    def _calculate_task_summary(self, tasks: list) -> dict:
        """Calculate task summary statistics."""
        total_tasks = len(tasks)
        by_status = {}
        pending_tasks = []
        in_progress_tasks = []
        completed_tasks = []
        failed_tasks = []

        for task in tasks:
            status = task["status"]
            by_status[status] = by_status.get(status, 0) + 1

            if status == "pending":
                pending_tasks.append(task)
            elif status == "in_progress":
                in_progress_tasks.append(task)
            elif status == "completed":
                completed_tasks.append(task)
            elif status == "failed":
                failed_tasks.append(task)

        return {
            "total_tasks": total_tasks,
            "by_status": by_status,
            "pending_tasks": pending_tasks,
            "in_progress_tasks": in_progress_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
        }

    def get_all_session_tasks(self, session_id: str) -> dict:
        """Get all tasks from all runs for a session."""
        # Write-through cache: Check memory first, then database
        if session_id not in self.session_tasks:
            logging.info(
                f"Session {session_id} not in cache, loading from database for get_all_session_tasks"
            )
            self._load_session_tasks_from_db(session_id)

        if session_id not in self.session_tasks:
            return {
                "session_id": session_id,
                "runs": {},
                "all_tasks": [],
                "summary": {
                    "total_tasks": 0,
                    "total_runs": 0,
                    "by_status": {},
                    "pending_tasks": [],
                    "in_progress_tasks": [],
                    "completed_tasks": [],
                    "failed_tasks": [],
                },
                "message": "No active plan for this session",
                "agent_type": "langgraph",
            }

        session_data = self.session_tasks[session_id]
        runs_data = session_data.get("runs", {})
        current_run_id = session_data.get("current_run_id")

        # Build clean runs structure
        runs = []
        current_tasks = []

        for run_id, run_data in runs_data.items():
            tasks = run_data.get("tasks", [])
            run_info = {
                "run_id": run_id,
                "tasks": tasks,
                "created_at": run_data.get("created_at"),
                "status": run_data.get("status", "active"),
                "question": run_data.get("question", ""),
                "is_current": run_id == current_run_id,
            }
            runs.append(run_info)

            # Set current tasks
            if run_id == current_run_id:
                current_tasks = tasks

        # Sort runs by creation date (most recent first)
        # created_at should never be missing - will throw KeyError if missing
        for run in runs:
            if not run.get("created_at"):
                logging.error(
                    f"Run {run['run_id']} missing created_at in get_all_session_tasks"
                )

        runs.sort(key=lambda x: x["created_at"], reverse=True)

        return {
            "session_id": session_id,
            "current_run_id": current_run_id,
            "tasks": current_tasks,  # Current run tasks
            "runs": runs,  # All runs with metadata
            "summary": self._calculate_task_summary(current_tasks),
            "total_runs": len(runs),
            "agent_type": "langgraph",
        }

    def _update_session_tasks(
        self,
        session_id: str,
        run_id: str,
        plan_steps: list = None,
        current_step: str = None,
        completed_step: str = None,
        step_result: str = None,
        question: str = None,
    ):
        """Update session task tracking for UI with proper run_id isolation."""
        import datetime

        current_time = datetime.datetime.utcnow()

        # Initialize session structure if it doesn't exist
        if session_id not in self.session_tasks:
            self.session_tasks[session_id] = {
                "runs": {},
                "current_run_id": None,
                "created_at": current_time,
            }

        session_data = self.session_tasks[session_id]

        # If plan_steps is provided, create a new run with its own tasks
        if plan_steps:
            # Initialize new run with its own task list
            tasks = []
            for i, step in enumerate(plan_steps):
                tasks.append(
                    {
                        "id": f"step_{i+1}",
                        "description": (
                            step[:100] + "..." if len(step) > 100 else step
                        ),
                        "full_description": step,
                        "status": "pending",
                        "priority": "medium",
                        "created_at": current_time,
                        "updated_at": current_time,
                        "result": None,
                        "run_id": run_id,  # Associate task with run_id
                    }
                )

            # Create new run entry
            session_data["runs"][run_id] = {
                "tasks": tasks,
                "created_at": current_time,
                "last_updated": current_time,
                "question": question or "",
                "status": "active",
            }

            # Set this as the current active run
            session_data["current_run_id"] = run_id

            # Write-through cache: persist to database
            self._persist_run_tasks_to_db(session_id, run_id, tasks, question)
            return

        # If no plan_steps, update existing run tasks
        if run_id not in session_data["runs"]:
            return

        run_data = session_data["runs"][run_id]

        # Update current step to in_progress
        if current_step:
            for task in run_data["tasks"]:
                if (
                    task["full_description"] == current_step
                    and task["status"] == "pending"
                ):
                    task["status"] = "in_progress"
                    task["updated_at"] = current_time
                    break

        # Mark completed step
        if completed_step and step_result:
            for task in run_data["tasks"]:
                if task["full_description"] == completed_step:
                    task["status"] = "completed"
                    task["updated_at"] = current_time
                    task["result"] = (
                        step_result[:200] + "..."
                        if len(step_result) > 200
                        else step_result
                    )
                    break

        run_data["last_updated"] = current_time

        # Write-through cache: persist updated tasks to database
        self._persist_run_tasks_to_db(session_id, run_id, run_data["tasks"])

    def _persist_run_tasks_to_db(
        self, session_id: str, run_id: str, tasks: list, question: str = None
    ):
        """Persist run tasks to database (write-through cache pattern)."""
        try:
            success = self.database_manager.store_run_tasks(session_id, run_id, tasks)
            if success:
                logging.info(
                    f"Persisted {len(tasks)} tasks for run {run_id} to database"
                )
            else:
                logging.error(f"Failed to persist tasks for run {run_id} to database")
        except Exception as e:
            logging.exception(
                f"Error persisting tasks to database for session {session_id}, run {run_id}: {e}"
            )

    def _mark_run_completed(self, session_id: str, run_id: str, success: bool = True):
        """Mark a run as completed or failed."""
        if session_id not in self.session_tasks:
            return

        session_data = self.session_tasks[session_id]
        if run_id not in session_data.get("runs", {}):
            return

        import datetime

        current_time = datetime.datetime.utcnow()

        run_data = session_data["runs"][run_id]
        run_data["status"] = "completed" if success else "failed"
        run_data["last_updated"] = current_time

        # Clear current_run_id since this run is now complete
        if session_data.get("current_run_id") == run_id:
            session_data["current_run_id"] = None
            logging.info(
                f"Cleared current_run_id for session {session_id} as run {run_id} completed"
            )

            # Clear current_run_id in database too
            try:
                self.database_manager.clear_current_run_id(session_id)
            except Exception as e:
                logging.exception(
                    f"Failed to clear current_run_id in database for session {session_id}: {e}"
                )

        # Write-through cache: persist completion status to database
        self._persist_run_tasks_to_db(session_id, run_id, run_data["tasks"])

    def get_session_run_history(self, session_id: str) -> dict:
        """Get all runs for a session with their task summaries."""
        # Write-through cache: Check memory first, then database
        if session_id not in self.session_tasks:
            logging.info(
                f"Session {session_id} not in cache, loading from database for get_session_run_history"
            )
            self._load_session_tasks_from_db(session_id)

        if session_id not in self.session_tasks:
            return {
                "session_id": session_id,
                "runs": [],
                "current_run_id": None,
                "total_runs": 0,
            }

        session_data = self.session_tasks[session_id]
        runs = []

        for run_id, run_data in session_data.get("runs", {}).items():
            run_summary = {
                "run_id": run_id,
                "question": run_data.get("question", ""),
                "status": run_data.get("status", "unknown"),
                "created_at": run_data.get("created_at"),
                "last_updated": run_data.get("last_updated"),
                "task_count": len(run_data.get("tasks", [])),
                "completed_tasks": len(
                    [
                        t
                        for t in run_data.get("tasks", [])
                        if t.get("status") == "completed"
                    ]
                ),
            }
            runs.append(run_summary)

        # Sort runs by creation time (newest first)
        # created_at should never be missing - will throw KeyError if missing
        runs.sort(key=lambda x: x["created_at"], reverse=True)

        return {
            "session_id": session_id,
            "runs": runs,
            "current_run_id": session_data.get("current_run_id"),
            "total_runs": len(runs),
        }

    def _create_google_genai_tools_DEPRECATED(self):
        """Create Google GenAI tool definitions (same as POC approach)."""
        mongodb_tool = types.Tool(
            function_declarations=[
                types.FunctionDeclaration(
                    name="query_mongodb",
                    description="Execute a MongoDB query on the log events collection to retrieve and analyze application logs. Use find() for simple queries and aggregate() for complex analysis with grouping, calculations, or transformations.",
                    parameters=types.Schema(
                        type=types.Type.OBJECT,
                        properties={
                            "query_type": types.Schema(
                                type=types.Type.STRING,
                                enum=["find", "aggregate"],
                                description="Type of MongoDB operation. Use 'find' for simple filtering and retrieval, 'aggregate' for complex analysis, grouping, calculations, or data transformations.",
                            ),
                            "filter": types.Schema(
                                type=types.Type.OBJECT,
                                description="MongoDB filter criteria as a JSON object",
                            ),
                            "projection": types.Schema(
                                type=types.Type.OBJECT,
                                description="Fields to include/exclude in results",
                            ),
                            "sort": types.Schema(
                                type=types.Type.OBJECT,
                                description="Sort criteria as field-direction pairs",
                            ),
                            "limit": types.Schema(
                                type=types.Type.INTEGER,
                                description="Maximum number of documents to return",
                            ),
                            "skip": types.Schema(
                                type=types.Type.INTEGER,
                                description="Number of documents to skip",
                            ),
                            "pipeline": types.Schema(
                                type=types.Type.ARRAY,
                                description="MongoDB aggregation pipeline stages",
                                items=types.Schema(type=types.Type.OBJECT),
                            ),
                        },
                        required=[],
                    ),
                )
            ]
        )
        return [mongodb_tool]

    def _convert_messages_to_genai(self, messages: list[BaseMessage]) -> list:
        """Convert LangChain messages to Google GenAI format (same as POC)."""
        genai_contents = []

        if not messages:
            return genai_contents

        for message in messages:
            try:
                if isinstance(message, HumanMessage):
                    content = types.Content(
                        role="user", parts=[types.Part(text=message.content)]
                    )
                    genai_contents.append(content)
                elif isinstance(message, AIMessage):
                    parts = []

                    # Add text content
                    if message.content:
                        parts.append(types.Part(text=message.content))

                    # Add function calls if present
                    if hasattr(message, "tool_calls") and message.tool_calls:
                        for tool_call in message.tool_calls:
                            # Convert to Google GenAI function call format
                            func_call = types.Part(
                                function_call=types.FunctionCall(
                                    name=tool_call["name"], args=tool_call["args"]
                                )
                            )
                            parts.append(func_call)

                    if parts:
                        content = types.Content(role="model", parts=parts)
                        genai_contents.append(content)

                elif isinstance(message, ToolMessage):
                    # Convert tool message to function response
                    func_response = types.Part(
                        function_response=types.FunctionResponse(
                            name=(
                                message.name if hasattr(message, "name") else "unknown"
                            ),
                            response={"content": message.content},
                        )
                    )
                    genai_contents.append(func_response)
            except Exception as e:
                logging.exception(
                    f"Error converting message to GenAI format: {e}, message: {message}"
                )
                continue

        return genai_contents

    def _convert_genai_response_to_message(self, response) -> AIMessage:
        """Convert Google GenAI response to LangChain AIMessage (same as POC)."""
        # Handle None response
        if response is None:
            logging.warning("🤖 Model returned None response")
            return AIMessage(content="No response from model (None response)")

        # Handle missing candidates
        if not hasattr(response, "candidates") or not response.candidates:
            logging.warning("🤖 Model response has no candidates")
            return AIMessage(content="No response from model (no candidates)")

        candidate = response.candidates[0]

        # Handle missing content
        if not hasattr(candidate, "content") or not candidate.content:
            logging.warning("🤖 Model candidate has no content")
            return AIMessage(content="No response from model (no content)")

        # Handle missing parts
        if not hasattr(candidate.content, "parts") or not candidate.content.parts:
            logging.warning("🤖 Model candidate content has no parts")
            return AIMessage(content="No response from model (no parts)")

        content_parts = candidate.content.parts

        text_content = ""
        tool_calls = []

        for part in content_parts:
            if hasattr(part, "text") and part.text:
                text_content += part.text
            elif hasattr(part, "function_call") and part.function_call:
                # Convert function call to LangChain format
                tool_call = {
                    "name": part.function_call.name,
                    "args": (
                        dict(part.function_call.args) if part.function_call.args else {}
                    ),
                    "id": f"call_{len(tool_calls)}",
                }
                tool_calls.append(tool_call)

                # Log the tool call being made by the model
                logging.info(
                    "🤖 Model generated tool call",
                    extra={
                        "details": {
                            "tool_name": part.function_call.name,
                            "tool_args": LoggingUtils.truncate_for_logging(
                                (
                                    dict(part.function_call.args)
                                    if part.function_call.args
                                    else {}
                                ),
                                500,
                            ),
                            "tool_call_id": f"call_{len(tool_calls)}",
                        }
                    },
                )

        # Log the complete model response
        logging.info(
            "🤖 Model response converted to AIMessage",
            extra={
                "details": {
                    "has_text_content": bool(text_content),
                    "text_content_length": len(text_content) if text_content else 0,
                    "tool_calls_count": len(tool_calls),
                    "tool_names": (
                        [tc["name"] for tc in tool_calls] if tool_calls else []
                    ),
                }
            },
        )

        # Create AI message with tool calls
        if tool_calls:
            return AIMessage(content=text_content or "", tool_calls=tool_calls)
        else:
            return AIMessage(content=text_content or "No response generated")

    def _validate_mongodb_args(self, args):
        """Validate MongoDB query arguments with comprehensive checks."""
        errors = []

        query_type = args.get("query_type", "find")

        # Validate query_type
        if query_type not in ["find", "aggregate"]:
            errors.append(
                f"Invalid query_type '{query_type}'. Must be 'find' or 'aggregate'"
            )

        # Validate and convert limit
        limit = args.get("limit")
        if limit is None:
            # None is valid, will use default limit in database manager
            pass
        elif isinstance(limit, float) and limit.is_integer():
            limit = int(limit)
            args["limit"] = limit
        elif not isinstance(limit, int) or limit < 1 or limit > 1000:
            errors.append(f"Invalid limit '{limit}'. Must be integer between 1-1000")

        # Validate and convert skip
        skip = args.get("skip")
        if skip is None:
            # None is valid, will use default in database manager
            pass
        elif isinstance(skip, float) and skip.is_integer():
            skip = int(skip)
            args["skip"] = skip
        elif not isinstance(skip, int) or skip < 0 or skip > 10000:
            errors.append(f"Invalid skip '{skip}'. Must be integer between 0-10000")

        # Query type specific validations
        if query_type == "aggregate":
            # Aggregate queries require pipeline
            pipeline = args.get("pipeline")
            if not pipeline:
                errors.append("pipeline is required when query_type='aggregate'")
            elif not isinstance(pipeline, list):
                errors.append("pipeline must be an array of objects")
            elif len(pipeline) < 1 or len(pipeline) > 20:
                errors.append("pipeline must contain 1-20 stages")
            elif not all(isinstance(stage, dict) for stage in pipeline):
                errors.append("All pipeline stages must be objects")

            # Aggregate queries should not use find-specific parameters
            find_only_params = ["filter", "projection", "sort", "skip"]
            for param in find_only_params:
                if param in args:
                    errors.append(
                        f"'{param}' parameter not allowed with aggregate queries"
                    )

        elif query_type == "find":
            # Find queries should not use aggregate-specific parameters
            if "pipeline" in args:
                errors.append("'pipeline' parameter not allowed with find queries")

            # Validate sort object structure
            sort_obj = args.get("sort")
            if sort_obj is not None:
                if not isinstance(sort_obj, dict):
                    errors.append("sort must be an object with field-direction pairs")
                else:
                    for field, direction in sort_obj.items():
                        # Convert float directions to integers if they're whole numbers
                        if isinstance(direction, float) and direction.is_integer():
                            direction = int(direction)
                            sort_obj[field] = direction
                        if direction not in [-1, 1]:
                            errors.append(
                                f"sort direction for '{field}' must be 1 (asc) or -1 (desc)"
                            )

        # Validate object types for filter and projection
        for param_name in ["filter", "projection"]:
            param_value = args.get(param_name)
            if param_value is not None and not isinstance(param_value, dict):
                errors.append(f"'{param_name}' must be an object")

        # Performance warnings (not errors, but logged)
        if limit and limit > 100:
            logging.warning(
                f"Large limit requested: {limit}. Consider using smaller chunks for better performance."
            )

        if skip and skip > 1000:
            logging.warning(
                f"Large skip value: {skip}. Consider using more efficient pagination methods."
            )

        return errors

    def _analyze_query_response_size(
        self, tool_response, function_args, session_id=None, api_usage_metadata=None
    ):
        """Analyze MongoDB query response size to determine if strategy guidance is needed."""
        response_str = str(tool_response)
        response_length = len(response_str)

        # Calculate estimated tokens for this tool response
        estimated_tool_response_tokens = response_length // 4

        # Ensure we have valid integers for calculation
        estimated_tool_response_tokens = estimated_tool_response_tokens or 0

        # Use just the tool response size for decision (simplified approach)
        decision_tokens = estimated_tool_response_tokens

        # Get current API usage metadata for logging if available
        api_token_metrics = {}
        if api_usage_metadata:
            api_token_metrics = {
                "current_prompt_tokens": getattr(
                    api_usage_metadata, "prompt_token_count", 0
                )
                or 0,
                "current_response_tokens": getattr(
                    api_usage_metadata, "candidates_token_count", 0
                )
                or 0,
                "current_total_tokens": getattr(
                    api_usage_metadata, "total_token_count", 0
                )
                or 0,
                "cached_content_tokens": getattr(
                    api_usage_metadata, "cached_content_token_count", 0
                )
                or 0,
            }

        # Tool response size thresholds (conservative)
        strategy_threshold = (
            200000  # 200k tokens - when to provide strategy guidance for tool responses
        )

        # Determine if this is a large dataset that needs strategy guidance
        needs_strategy = decision_tokens > strategy_threshold

        # Get result count for logging
        result_count = len(tool_response) if isinstance(tool_response, list) else 1

        # Initialize strategy_reason for all cases
        strategy_reason = None

        if needs_strategy:
            strategy_reason = f"Large tool response detected: {decision_tokens:,} estimated tokens from {result_count} results"

            # Enhanced logging for large datasets
            logging_details = {
                "decision_tokens": decision_tokens,
                "strategy_threshold": strategy_threshold,
                "estimated_tool_tokens": estimated_tool_response_tokens,
                "response_length": response_length,
                "result_count": result_count,
                "result_type": type(tool_response).__name__,
                "strategy_reason": strategy_reason,
                "query_args": LoggingUtils.truncate_for_logging(function_args, 300),
                "token_source": "tool_response_estimation",
                "api_token_metrics": api_token_metrics,
                "large_dataset_strategy": "guidance_will_be_provided",
            }

            # Log large dataset detection
            logging.warning(
                "🚨 LARGE DATASET DETECTED - Strategy guidance will be provided",
                extra={"details": logging_details},
            )
        else:
            # Enhanced logging for normal responses
            logging_details = {
                "decision_tokens": decision_tokens,
                "strategy_threshold": strategy_threshold,
                "estimated_tool_tokens": estimated_tool_response_tokens,
                "response_length": response_length,
                "result_count": result_count,
                "result_type": type(tool_response).__name__,
                "needs_strategy": False,
                "within_limits": True,
                "token_source": "tool_response_estimation",
                "api_token_metrics": api_token_metrics,
            }

            # Log normal response size for monitoring
            logging.info(
                "MongoDB query response size analysis",
                extra={"details": logging_details},
            )

        return {
            "needs_strategy": needs_strategy,
            "strategy_reason": strategy_reason,
            "decision_tokens": decision_tokens,
            "estimated_tokens": estimated_tool_response_tokens,
            "response_length": response_length,
            "result_count": result_count,
            "token_source": "tool_response_estimation",
            "api_token_metrics": api_token_metrics,
        }

    def _create_large_dataset_response(
        self, tool_response, response_analysis, return_to_model=False
    ):
        """Create a response with strategy guidance for large datasets or return to model for processing."""
        # Get decision tokens and thresholds
        decision_tokens = response_analysis.get("decision_tokens", 0)
        context_window_limit = 1048576  # 1M tokens
        critical_threshold = context_window_limit * 0.9  # 90% of limit

        # Check if we're approaching critical token limits
        approaching_limit = decision_tokens > critical_threshold

        # Log the strategy response creation
        logging.info(
            "📋 Creating large dataset strategy response",
            extra={
                "details": {
                    "decision_tokens": decision_tokens,
                    "critical_threshold": critical_threshold,
                    "approaching_limit": approaching_limit,
                    "estimated_tokens": response_analysis["estimated_tokens"],
                    "response_length": response_analysis["response_length"],
                    "result_count": response_analysis["result_count"],
                    "return_to_model": return_to_model,
                    "strategy_reason": response_analysis["strategy_reason"],
                }
            },
        )

        # If we're not approaching critical limits and return_to_model is True,
        # return the full response to let the model process it
        if return_to_model and not approaching_limit:
            logging.info(
                "📤 Returning large dataset to model for processing",
                extra={
                    "details": {
                        "decision_tokens": decision_tokens,
                        "within_safe_limits": True,
                        "response_length": response_analysis["response_length"],
                        "result_count": response_analysis["result_count"],
                    }
                },
            )
            return str(tool_response)

        # Create strategy guidance message
        strategy_message = f"""
🚨 LARGE DATASET DETECTED 🚨

The MongoDB query returned a large amount of data that may exceed context limits:
- {response_analysis['strategy_reason']}
- Decision tokens: {decision_tokens:,}
- Response size: {response_analysis['response_length']:,} characters

RECOMMENDED STRATEGIES:
1. Use projection to get only summary fields: projection(['_id', 'summary', 'timestamp'])
2. Start with overview queries, then drill down to specific logs by _id
3. Add more specific filters to reduce the dataset size
4. Consider time-based filtering to narrow the scope

CURRENT QUERY DETAILS:
- Query type: {tool_response.__class__.__name__}
- Result count: {response_analysis['result_count']}
- Token source: {response_analysis.get('token_source', 'unknown')}

{"CRITICAL: Approaching context window limit!" if approaching_limit else "Query completed successfully, consider optimization strategies."}

Raw query results follow below (truncated if too large):
"""

        # Handle truncation only if approaching critical token limits
        if approaching_limit:
            # Truncate to prevent context overflow
            truncate_length = 20000
            truncated_response = (
                str(tool_response)[:truncate_length]
                + f"\n\n... [RESPONSE TRUNCATED DUE TO CRITICAL TOKEN LIMITS] ..."
            )
            final_response = strategy_message + "\n" + truncated_response

            logging.warning(
                "📄 Large dataset response was truncated to prevent context overflow",
                extra={
                    "details": {
                        "original_length": response_analysis["response_length"],
                        "truncated_length": len(truncated_response),
                        "final_response_length": len(final_response),
                        "result_count": response_analysis["result_count"],
                        "truncation_reason": "critical_token_limits",
                        "decision_tokens": decision_tokens,
                        "critical_threshold": critical_threshold,
                    }
                },
            )

            return final_response
        else:
            # No truncation needed - return strategy message with full response
            final_response = strategy_message + "\n" + str(tool_response)
            return final_response

    def query_mongodb(
        self,
        query_type: str,
        filter: dict = None,
        projection: dict = None,
        sort: dict = None,
        limit: int = None,
        skip: int = None,
        pipeline: List[dict] = None,
    ) -> str:
        """
        Execute MongoDB queries on log events collection.

        This replaces all custom task management tools with a single, comprehensive tool
        that includes the same validation and execution logic as the original.

        Args:
            query_type: 'find' or 'aggregate'
            filter: MongoDB filter criteria
            projection: Fields to include/exclude
            sort: Sort criteria
            limit: Maximum documents to return (1-1000)
            skip: Documents to skip for pagination
            pipeline: Aggregation pipeline for complex queries

        Returns:
            String representation of query results
        """
        # Debug logging to trace tool calls
        logging.info(
            f"🔧 query_mongodb tool called with query_type='{query_type}', filter={filter}"
        )
        tool_start_time = time.time()

        # Prepare function arguments
        function_args = {"query_type": query_type}

        # Add optional parameters only if they are not None
        if filter:
            function_args["filter"] = filter
        if projection:
            function_args["projection"] = projection
        if sort:
            function_args["sort"] = sort
        if limit is not None:
            function_args["limit"] = limit
        if skip is not None:
            function_args["skip"] = skip
        if pipeline:
            function_args["pipeline"] = pipeline

        try:
            # Runtime validation of function arguments
            validation_errors = self._validate_mongodb_args(function_args)
            if validation_errors:
                raise ValueError(f"Invalid arguments: {'; '.join(validation_errors)}")

            # Use the summarized collection for agent queries (contains logs with AI summaries)
            function_args["collection"] = (
                self.database_manager.summarized_collection_name
            )
            tool_response = self.database_manager.query_mongodb(**function_args)
            tool_execution_time = (time.time() - tool_start_time) * 1000

            # Enhanced tool response logging
            LoggingUtils.log_tool_execution(
                "query_mongodb",
                function_args,
                tool_response,
                execution_time=tool_execution_time,
            )

            # Analyze response size and provide strategy guidance with session context
            response_analysis = self._analyze_query_response_size(
                tool_response, function_args, session_id=self._current_session_id
            )

            # Create the response content with strategy guidance if needed
            if response_analysis["needs_strategy"]:
                # Determine if we should return large dataset to model based on token analysis
                context_window_limit = 1048576  # 1M tokens
                critical_threshold = context_window_limit * 0.9  # 90% of limit
                return_to_model = (
                    response_analysis["decision_tokens"] < critical_threshold
                )

                response_content = self._create_large_dataset_response(
                    tool_response, response_analysis, return_to_model=return_to_model
                )
            else:
                response_content = str(tool_response)

            # Additional response analytics
            response_analytics = {
                "response_length": len(response_content),
                "execution_time_ms": tool_execution_time,
                "large_dataset_detected": response_analysis["needs_strategy"],
                "estimated_token_count": response_analysis["estimated_tokens"],
            }

            if isinstance(tool_response, list):
                response_analytics["result_count"] = len(tool_response)
                response_analytics["result_type"] = "list"
            elif isinstance(tool_response, dict):
                response_analytics["result_type"] = "dict"
                response_analytics["result_keys"] = list(tool_response.keys())[:5]

            logging.info(
                "Tool response analytics",
                extra={"details": response_analytics},
            )

            return response_content

        except Exception as e:
            tool_execution_time = (time.time() - tool_start_time) * 1000

            # Enhanced error logging
            LoggingUtils.log_tool_execution(
                "query_mongodb",
                function_args,
                None,
                execution_time=tool_execution_time,
                error=e,
            )

            error_content = f"MongoDB query failed: {str(e)}"
            logging.exception(error_content)
            return error_content

    def _create_workflow(self) -> StateGraph:
        """Create the Plan-and-Execute workflow."""

        from langchain_core.tools import tool

        # Create tool function with proper closure to avoid binding issues
        database_manager = self.database_manager
        validate_mongodb_args = self._validate_mongodb_args
        analyze_query_response_size = self._analyze_query_response_size
        create_large_dataset_response = self._create_large_dataset_response

        @tool("query_mongodb")
        def query_mongodb_tool(
            query_type: str,
            filter: dict = None,
            projection: dict = None,
            sort: dict = None,
            limit: int = None,
            skip: int = None,
            pipeline: List[dict] = None,
        ) -> str:
            """
            Execute a MongoDB query on the log events collection to retrieve and analyze application logs. Use find() for simple queries and aggregate() for complex analysis with grouping, calculations, or transformations.

            Args:
                query_type: Type of MongoDB operation. Use 'find' for simple filtering and retrieval, 'aggregate' for complex analysis, grouping, calculations, or data transformations.
                filter: MongoDB filter criteria as a JSON object (e.g., {'context.application_id': 'APP123', 'status_code': {'$gte': 400}}). Only used with query_type='find'. Use dot notation for nested fields.
                projection: Fields to include/exclude in results (e.g., {'_id': 0, 'context.application_id': 1, 'response.body': 1}). Only used with query_type='find'. Use 1 to include, 0 to exclude fields.
                sort: Sort criteria as field-direction pairs (e.g., {'timestamp': -1, 'context.application_id': 1}). Use 1 for ascending, -1 for descending. Only used with query_type='find'.
                limit: Maximum number of documents to return. Range: 1-1000. Use smaller limits for performance and readability.
                skip: Number of documents to skip for pagination. Range: 0-10000. Only used with query_type='find'.
                pipeline: MongoDB aggregation pipeline stages (e.g., [{'$match': {...}}, {'$group': {...}}, {'$sort': {...}}]). Required when query_type='aggregate'. Each stage must be a valid MongoDB aggregation operation.

            Returns:
                String representation of query results.
            """
            # Debug logging to trace tool calls
            logging.info(
                f"🔧 query_mongodb tool called with query_type='{query_type}', filter={filter}"
            )
            tool_start_time = time.time()

            # Prepare function arguments
            function_args = {"query_type": query_type}

            # Add optional parameters only if they are not None
            if filter:
                function_args["filter"] = filter
            if projection:
                function_args["projection"] = projection
            if sort:
                function_args["sort"] = sort
            if limit is not None:
                function_args["limit"] = limit
            if skip is not None:
                function_args["skip"] = skip
            if pipeline:
                function_args["pipeline"] = pipeline

            try:
                # Runtime validation of function arguments
                validation_errors = validate_mongodb_args(function_args)
                if validation_errors:
                    raise ValueError(
                        f"Invalid arguments: {'; '.join(validation_errors)}"
                    )

                # Use the summarized collection for agent queries (contains logs with AI summaries)
                function_args["collection"] = (
                    database_manager.summarized_collection_name
                )
                tool_response = database_manager.query_mongodb(**function_args)
                tool_execution_time = (time.time() - tool_start_time) * 1000

                # Enhanced tool response logging
                LoggingUtils.log_tool_execution(
                    "query_mongodb",
                    function_args,
                    tool_response,
                    execution_time=tool_execution_time,
                )

                # Analyze response size and provide strategy guidance
                response_analysis = analyze_query_response_size(
                    tool_response, function_args
                )

                # Create the response content with strategy guidance if needed
                if response_analysis["needs_strategy"]:
                    response_content = create_large_dataset_response(
                        tool_response, response_analysis
                    )
                else:
                    response_content = str(tool_response)

                # Additional response analytics
                response_analytics = {
                    "response_length": len(response_content),
                    "execution_time_ms": tool_execution_time,
                    "large_dataset_detected": response_analysis["needs_strategy"],
                    "estimated_token_count": response_analysis["estimated_tokens"],
                }

                if isinstance(tool_response, list):
                    response_analytics["result_count"] = len(tool_response)
                    response_analytics["result_type"] = "list"
                elif isinstance(tool_response, dict):
                    response_analytics["result_type"] = "dict"
                    response_analytics["result_keys"] = list(tool_response.keys())[:5]

                logging.info(
                    "Tool response analytics",
                    extra={"details": response_analytics},
                )

                return response_content

            except Exception as e:
                tool_execution_time = (time.time() - tool_start_time) * 1000

                # Enhanced error logging
                LoggingUtils.log_tool_execution(
                    "query_mongodb",
                    function_args,
                    None,
                    execution_time=tool_execution_time,
                    error=e,
                )

                error_content = f"MongoDB query failed: {str(e)}"
                logging.exception(error_content)
                return error_content

        # Tools available to the agent
        tools = [query_mongodb_tool]
        tool_node = ToolNode(tools)

        # Debug: Add logging to see when ToolNode is called and post-process for token analysis
        def debug_tool_node(state):
            messages = state.get("messages", [])
            last_message = messages[-1] if messages else None
            session_id = state.get("session_id")

            # Get current run information for analysis
            run_id = state.get("run_id")
            run_cost_summary = self.get_run_cost_summary(run_id) if run_id else None

            # Log tool execution initiation with detailed inputs
            if (
                isinstance(last_message, AIMessage)
                and hasattr(last_message, "tool_calls")
                and last_message.tool_calls
            ):
                for tool_call in last_message.tool_calls:
                    logging.info(
                        "🔧 Tool execution initiated",
                        extra={
                            "details": {
                                "tool_name": tool_call["name"],
                                "tool_call_id": tool_call.get("id", "unknown"),
                                "input_arguments": LoggingUtils.truncate_for_logging(
                                    tool_call.get("args", {}), 1000
                                ),
                                "state_session_id": session_id,
                                "current_step": state.get("current_step", "unknown"),
                                "run_cost_summary": run_cost_summary,
                            }
                        },
                    )

            logging.info(f"🔧 ToolNode executing with {len(tools)} tools registered")

            # Set current session context for tool execution
            self._current_session_id = session_id

            try:
                result = tool_node.invoke(state)

                # Post-process tool results for comprehensive token analysis and large dataset handling
                if hasattr(result, "get") and result.get("messages"):
                    for msg in result["messages"]:
                        if hasattr(msg, "content") and msg.content:
                            # Analyze tool response with current run context
                            if run_cost_summary:
                                estimated_tokens = len(msg.content) // 4
                                past_usage_tokens = run_cost_summary.get(
                                    "total_input_tokens", 0
                                ) + run_cost_summary.get("total_output_tokens", 0)
                                projected_tokens = past_usage_tokens + estimated_tokens

                                # Context window limits
                                context_window_limit = 1048576  # 1M tokens
                                strategy_threshold = 800000  # 800k tokens
                                critical_threshold = (
                                    context_window_limit * 0.9
                                )  # 90% of limit

                                # Check if this is a large dataset response that needs special handling
                                if "🚨 LARGE DATASET DETECTED" in msg.content:
                                    # Check if we can safely return the large dataset to the model for processing
                                    if projected_tokens < critical_threshold:
                                        # Safe to return large dataset to model - extract raw data if possible
                                        lines = msg.content.split("\n")
                                        raw_data_start = -1
                                        for i, line in enumerate(lines):
                                            if (
                                                "Raw query results follow" in line
                                                or "[{" in line
                                            ):
                                                raw_data_start = i
                                                break

                                        if raw_data_start > 0:
                                            # Extract just the raw data without the strategy guidance
                                            raw_data_lines = lines[raw_data_start:]
                                            raw_data = "\n".join(raw_data_lines)
                                            # Remove truncation markers if present
                                            if "... [RESPONSE TRUNCATED" in raw_data:
                                                truncation_index = raw_data.find(
                                                    "... [RESPONSE TRUNCATED"
                                                )
                                                raw_data = raw_data[:truncation_index]

                                            # Replace the message content with raw data for model processing
                                            msg.content = raw_data

                                            logging.info(
                                                "🔧 Large dataset returned to model for processing",
                                                extra={
                                                    "details": {
                                                        "past_usage_tokens": past_usage_tokens,
                                                        "estimated_response_tokens": estimated_tokens,
                                                        "projected_total_tokens": projected_tokens,
                                                        "critical_threshold": critical_threshold,
                                                        "within_safe_limits": True,
                                                        "raw_data_length": len(
                                                            raw_data
                                                        ),
                                                        "strategy_guidance_removed": True,
                                                    }
                                                },
                                            )
                                        else:
                                            # Couldn't extract raw data, keep strategy guidance
                                            logging.info(
                                                "🔧 Large dataset strategy guidance preserved",
                                                extra={
                                                    "details": {
                                                        "past_usage_tokens": past_usage_tokens,
                                                        "estimated_response_tokens": estimated_tokens,
                                                        "projected_total_tokens": projected_tokens,
                                                        "reason": "could_not_extract_raw_data",
                                                    }
                                                },
                                            )
                                    else:
                                        # Too close to limits, keep strategy guidance
                                        logging.warning(
                                            "🔧 Large dataset strategy guidance preserved due to token limits",
                                            extra={
                                                "details": {
                                                    "past_usage_tokens": past_usage_tokens,
                                                    "estimated_response_tokens": estimated_tokens,
                                                    "projected_total_tokens": projected_tokens,
                                                    "critical_threshold": critical_threshold,
                                                    "within_safe_limits": False,
                                                }
                                            },
                                        )
                                else:
                                    # Regular response - log token analysis
                                    logging.info(
                                        "🔧 Tool response token analysis",
                                        extra={
                                            "details": {
                                                "past_usage_tokens": past_usage_tokens,
                                                "estimated_response_tokens": estimated_tokens,
                                                "projected_total_tokens": projected_tokens,
                                                "strategy_threshold": strategy_threshold,
                                                "needs_strategy": projected_tokens
                                                > strategy_threshold,
                                                "within_safe_limits": projected_tokens
                                                < critical_threshold,
                                            }
                                        },
                                    )

                            # Log tool execution completion
                            logging.info(
                                "🔧 Tool execution completed",
                                extra={
                                    "details": {
                                        "output_content_length": (
                                            len(msg.content) if msg.content else 0
                                        ),
                                        "output_preview": (
                                            LoggingUtils.truncate_for_logging(
                                                msg.content, 200
                                            )
                                            if msg.content
                                            else "No content"
                                        ),
                                        "message_type": type(msg).__name__,
                                        "run_cost_summary": run_cost_summary,
                                    }
                                },
                            )

                return result
            except Exception as e:
                logging.exception(
                    "🔧 ToolNode execution failed",
                    extra={
                        "details": {
                            "error_message": str(e),
                            "error_type": type(e).__name__,
                            "state_session_id": state.get("session_id"),
                            "current_step": state.get("current_step", "unknown"),
                        }
                    },
                )
                raise
            finally:
                # Clear current session context after tool execution
                self._current_session_id = None

        # Create Google GenAI tools for model awareness, but execution goes through LangChain tools
        self.genai_tools = self._create_google_genai_tools_DEPRECATED()

        def plan_step(state: PlanExecuteState) -> Dict[str, Any]:
            """
            Planning step - creates a multi-step plan for log analysis.

            This replaces the custom add_tasks tool with automatic planning.
            """

            # Log planning step initiation
            logging.info(
                "🎯 Planning step initiated",
                extra={
                    "details": {
                        "user_input": LoggingUtils.truncate_for_logging(
                            state["input"], 500
                        ),
                        "session_id": state.get("session_id"),
                        "persona": state.get("persona", "business"),
                    }
                },
            )

            # Get base system prompt for LangGraph (removes task management complexity)
            base_system_prompt = self.prompt_manager.get_langgraph_system_prompt(
                mongodb_db=self.database_manager.database.name,
                collection_name=self.database_manager.summarized_collection_name,
                persona=state.get("persona", "business"),
            )

            # Add memory context to system prompt for informed planning
            memory_context_section = self._format_memory_context(
                state.get("memory_context", {})
            )
            system_prompt = base_system_prompt + memory_context_section

            # Log the final constructed system prompt
            logging.info(
                "📋 Final system prompt constructed for planning",
                extra={
                    "details": {
                        "base_prompt_length": len(base_system_prompt),
                        "memory_context_length": len(memory_context_section),
                        "total_prompt_length": len(system_prompt),
                        "has_memory_context": len(memory_context_section) > 0,
                        "full_system_prompt": system_prompt,
                    }
                },
            )

            # Include chat history context in planning if available
            history_section = ""
            if state.get("chat_history_context"):
                history_section = (
                    f"\n\nPREVIOUS CONVERSATION:\n{state['chat_history_context']}\n"
                )

            plan_prompt = f"""
            {system_prompt}

            PLANNING TASK:
            Create a step-by-step plan to answer this log analysis question: {state['input']}{history_section}

            Your plan should:
            1. Break down the question into logical phases of work
            2. Each step should represent a complete data gathering or analysis phase (MongoDB queries should be comprehensive, not split into parts)
            3. Build logically toward a comprehensive answer
            4. Be specific about what data to query - complex queries with filtering, sorting, grouping should be ONE step
            5. Consider the context from previous conversation if provided

            Return your plan as a numbered list of steps. Each step should be clear and actionable.
            """

            # Log the planning prompt being sent to model
            logging.info(
                "🎯 Sending planning prompt to model",
                extra={
                    "details": {
                        "prompt_length": len(plan_prompt),
                        "system_prompt_length": len(system_prompt),
                        "model_name": self.config.model_name,
                    }
                },
            )

            # Use Google GenAI client directly (same as POC and original agent)
            user_content = types.Content(
                role="user", parts=[types.Part(text=plan_prompt)]
            )

            try:
                # Check input size before API call
                input_analysis = self._check_input_size_before_api_call(
                    system_prompt, [user_content], "planning"
                )

                # Proceed with API call (even if large - let Gemini handle it)
                response = call_google_genai_with_retry(
                    client=self.client,
                    model=self.config.model_name,
                    contents=[user_content],
                    config=types.GenerateContentConfig(
                        system_instruction=system_prompt
                    ),
                )

                # Log API usage and cost for planning call
                session_id = state.get("session_id")
                run_id = state.get("run_id")
                if response:
                    self._log_api_usage_and_cost(
                        response, "planning", session_id, run_id
                    )

                # Log successful model response
                logging.info(
                    "🎯 Model responded to planning request",
                    extra={
                        "details": {
                            "has_response": response is not None,
                            "has_candidates": hasattr(response, "candidates")
                            and bool(response.candidates),
                            "run_cost_summary": (
                                self.get_run_cost_summary(run_id) if run_id else None
                            ),
                        }
                    },
                )
            except Exception as e:
                logging.exception(
                    "🎯 Planning model call failed",
                    extra={
                        "details": {
                            "error_message": str(e),
                            "error_type": type(e).__name__,
                        }
                    },
                )
                raise

            # Extract steps from the response with proper error handling
            plan_text = ""
            if response and hasattr(response, "text") and response.text:
                plan_text = response.text
            elif response and hasattr(response, "candidates") and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, "content") and candidate.content:
                    if hasattr(candidate.content, "parts") and candidate.content.parts:
                        for part in candidate.content.parts:
                            if hasattr(part, "text") and part.text:
                                plan_text += part.text

            if not plan_text:
                plan_text = "Unable to generate plan from model response"

            # Debug logging to see what plan was generated
            logging.info(f"🎯 Generated plan text: {plan_text}")

            # Simple parsing - look for numbered items
            steps = []
            for line in plan_text.split("\n"):
                line = line.strip()
                # Match patterns like "1. Step", "Step 1:", "- Step", etc.
                if line and (
                    line[0].isdigit()
                    or line.startswith("- ")
                    or line.startswith("• ")
                    or "step" in line.lower()[:10]
                ):
                    # Clean up the step text
                    step = line
                    # Remove leading numbers, bullets, etc.
                    for prefix in [
                        "1.",
                        "2.",
                        "3.",
                        "4.",
                        "5.",
                        "6.",
                        "7.",
                        "8.",
                        "9.",
                        "- ",
                        "• ",
                    ]:
                        if step.startswith(prefix):
                            step = step[len(prefix) :].strip()
                            break

                    if step and len(step) > 10:  # Avoid empty or very short steps
                        steps.append(step)

            # Debug logging to see what steps were extracted
            logging.info(f"📝 Extracted steps from plan: {steps}")

            # Fallback if parsing fails
            if not steps:
                steps = [
                    "Query MongoDB to retrieve relevant log data",
                    "Analyze the retrieved data for patterns and insights",
                    "Generate comprehensive findings and recommendations",
                ]
                logging.info("🔄 Using fallback steps")

            # Log the final plan creation with detailed analysis
            logging.info(
                "📋 Plan creation completed",
                extra={
                    "details": {
                        "steps_count": len(steps),
                        "plan_steps": LoggingUtils.truncate_for_logging(steps, 1000),
                        "original_input": LoggingUtils.truncate_for_logging(
                            state["input"], 300
                        ),
                        "plan_text_length": len(plan_text),
                        "parsing_successful": len(steps) > 0,
                        "used_fallback": len(steps) == 3
                        and steps[0].startswith("Query MongoDB"),
                    }
                },
            )

            # Update session tasks for UI
            session_id = state.get("session_id")
            run_id = state.get("run_id")
            if session_id and run_id:
                self._update_session_tasks(
                    session_id, run_id, plan_steps=steps, question=state.get("input")
                )

            return {"plan": steps}

        def consult_memory(state: PlanExecuteState) -> Dict[str, Any]:
            """
            Consult memory system for relevant knowledge based on user input.

            This replaces static 700+ line prompts with dynamic, targeted memory retrieval.
            """

            # Log memory consultation initiation
            logging.info(
                "🧠 Memory consultation initiated",
                extra={
                    "details": {
                        "user_input": LoggingUtils.truncate_for_logging(
                            state["input"], 500
                        ),
                        "session_id": state.get("session_id"),
                        "persona": state.get("persona", "business"),
                    }
                },
            )

            try:
                memory_manager = self.memory_manager
                user_query = state["input"]

                # Get ALL field knowledge and agent guidance (always useful)
                field_knowledge = memory_manager.get_memories_by_type(
                    "field_knowledge", limit=50
                )
                agent_guidance = memory_manager.get_memories_by_type(
                    "agent_guidance", limit=50
                )

                # Search semantically for domain workflows and translation mappings
                domain_workflows = memory_manager.search(
                    user_query, type_filter="domain_workflow", top_k=3
                )
                translation_mappings = memory_manager.search(
                    user_query, type_filter="translation_mapping", top_k=3
                )

                # Convert field_knowledge and agent_guidance to dict format (they return MemoryUnit objects)
                field_knowledge_dicts = (
                    [memory.to_dict() for memory in field_knowledge]
                    if field_knowledge
                    else []
                )
                agent_guidance_dicts = (
                    [memory.to_dict() for memory in agent_guidance]
                    if agent_guidance
                    else []
                )

                # Compile memory context
                memory_context = {
                    "domain_workflows": domain_workflows,
                    "field_knowledge": field_knowledge_dicts,
                    "translation_mappings": translation_mappings,
                    "agent_guidance": agent_guidance_dicts,
                }

                # Extract memory IDs for feedback tracking
                memory_ids_used = []
                for memory_type, memories in memory_context.items():
                    for memory in memories:
                        if "_id" in memory:
                            memory_ids_used.append(memory["_id"])

                # Extract memory IDs by type for detailed logging
                domain_workflow_ids = [
                    mem.get("_id") for mem in domain_workflows if "_id" in mem
                ]
                field_knowledge_ids = [
                    mem.get("_id") for mem in field_knowledge_dicts if "_id" in mem
                ]
                translation_mapping_ids = [
                    mem.get("_id") for mem in translation_mappings if "_id" in mem
                ]
                agent_guidance_ids = [
                    mem.get("_id") for mem in agent_guidance_dicts if "_id" in mem
                ]

                # Log memory consultation results with detailed breakdown
                total_memories_found = sum(
                    len(memories) for memories in memory_context.values()
                )
                logging.info(
                    "🧠 Memory consultation completed",
                    extra={
                        "details": {
                            "total_memories_found": total_memories_found,
                            "domain_workflows_count": len(domain_workflows),
                            "domain_workflow_ids": domain_workflow_ids,
                            "field_knowledge_count": len(field_knowledge_dicts),
                            "field_knowledge_ids": field_knowledge_ids,
                            "translation_mappings_count": len(translation_mappings),
                            "translation_mapping_ids": translation_mapping_ids,
                            "agent_guidance_count": len(agent_guidance_dicts),
                            "agent_guidance_ids": agent_guidance_ids,
                            "all_memory_ids_used": memory_ids_used,
                            "session_id": state.get("session_id"),
                        }
                    },
                )

                return {
                    "memory_context": memory_context,
                    "memory_ids_used": memory_ids_used,
                }

            except Exception as e:
                logging.error(
                    "❌ Memory consultation failed",
                    extra={
                        "details": {
                            "error_message": str(e),
                            "error_type": type(e).__name__,
                            "session_id": state.get("session_id"),
                        }
                    },
                )
                # Return empty memory context on failure - workflow should continue
                return {
                    "memory_context": {
                        "domain_workflows": [],
                        "field_knowledge": [],
                        "translation_mappings": [],
                        "agent_guidance": [],
                    },
                    "memory_ids_used": [],
                }

        def call_agent(state: PlanExecuteState) -> Dict[str, Any]:
            """
            Call the Google GenAI model with tools (same pattern as POC).
            """
            messages = state.get("messages", [])

            # Log agent call initiation
            logging.info(
                "🤖 Agent call initiated",
                extra={
                    "details": {
                        "messages_count": len(messages),
                        "session_id": state.get("session_id"),
                        "current_step": state.get("current_step", "unknown"),
                        "step_execution_count": state.get("step_execution_count", 0),
                        "tools_available": len(self.genai_tools),
                    }
                },
            )

            # Convert LangChain messages to Google GenAI format
            genai_contents = self._convert_messages_to_genai(messages)

            # Log message conversion
            logging.info(
                "🤖 Messages converted to GenAI format",
                extra={
                    "details": {
                        "original_messages_count": len(messages),
                        "converted_contents_count": len(genai_contents),
                        "last_message_type": (
                            type(messages[-1]).__name__ if messages else "None"
                        ),
                    }
                },
            )

            # Get base system prompt
            base_system_prompt = self.prompt_manager.get_langgraph_system_prompt(
                mongodb_db=self.database_manager.database.name,
                collection_name=self.database_manager.summarized_collection_name,
                persona=state.get("persona", "business"),
            )

            # Add memory context to system prompt
            memory_context_section = self._format_memory_context(
                state.get("memory_context", {})
            )
            system_prompt = base_system_prompt + memory_context_section

            # Log the final constructed system prompt
            logging.info(
                "📋 Final system prompt constructed for agent call",
                extra={
                    "details": {
                        "base_prompt_length": len(base_system_prompt),
                        "memory_context_length": len(memory_context_section),
                        "total_prompt_length": len(system_prompt),
                        "has_memory_context": len(memory_context_section) > 0,
                        "full_system_prompt": system_prompt,
                    }
                },
            )

            try:
                # Log model call
                logging.info(
                    "🤖 Calling Google GenAI model",
                    extra={
                        "details": {
                            "model_name": self.config.lite_model,  # Use lite model for step execution
                            "system_prompt_length": len(system_prompt),
                            "contents_count": len(genai_contents),
                            "tools_count": len(self.genai_tools),
                        }
                    },
                )

                # Check input size before API call
                input_analysis = self._check_input_size_before_api_call(
                    system_prompt, genai_contents, "agent"
                )

                # Proceed with API call (even if large - let Gemini handle it)
                response = call_google_genai_with_retry(
                    client=self.client,
                    model=self.config.lite_model,  # Use lite model for step execution
                    contents=genai_contents,
                    config=types.GenerateContentConfig(
                        tools=self.genai_tools, system_instruction=system_prompt
                    ),
                )

                # Log API usage and cost for agent call
                session_id = state.get("session_id")
                run_id = state.get("run_id")
                if response:
                    self._log_api_usage_and_cost(response, "agent", session_id, run_id)

                # Convert response back to LangChain format
                ai_message = self._convert_genai_response_to_message(response)

                # Log successful agent response
                logging.info(
                    "🤖 Agent response generated",
                    extra={
                        "details": {
                            "response_content_length": (
                                len(ai_message.content) if ai_message.content else 0
                            ),
                            "has_tool_calls": hasattr(ai_message, "tool_calls")
                            and bool(ai_message.tool_calls),
                            "tool_calls_count": (
                                len(ai_message.tool_calls)
                                if hasattr(ai_message, "tool_calls")
                                and ai_message.tool_calls
                                else 0
                            ),
                            "run_cost_summary": (
                                self.get_run_cost_summary(run_id) if run_id else None
                            ),
                        }
                    },
                )

                return {"messages": [ai_message]}

            except Exception as e:
                logging.exception(
                    "🤖 Agent call failed",
                    extra={
                        "details": {
                            "error_message": str(e),
                            "error_type": type(e).__name__,
                            "session_id": state.get("session_id"),
                            "current_step": state.get("current_step", "unknown"),
                        }
                    },
                )
                error_message = AIMessage(content=f"Error calling model: {str(e)}")
                return {"messages": [error_message]}

        def execute_step(state: PlanExecuteState) -> Dict[str, Any]:
            """
            Execute the next step in the plan.

            This creates a message and delegates to the agent.
            """
            # Log step execution initiation
            logging.info(
                "⚡ Step execution initiated",
                extra={
                    "details": {
                        "remaining_steps": len(state.get("plan", [])),
                        "completed_steps": len(state.get("past_steps", [])),
                        "session_id": state.get("session_id"),
                    }
                },
            )

            if not state["plan"]:
                logging.info("⚡ All steps completed - no more steps in plan")
                return {"response": "All steps completed"}

            current_step = state["plan"][0]
            remaining_plan = state["plan"][1:]

            # Check if we're still on the same step (prevent infinite loop)
            if state.get("current_step") == current_step:
                step_count = state.get("step_execution_count", 0) + 1
                if step_count > 5:  # Max 5 attempts per step
                    logging.warning(
                        "⚠️ Step execution limit reached",
                        extra={
                            "details": {
                                "step_text": LoggingUtils.truncate_for_logging(
                                    current_step, 200
                                ),
                                "execution_count": step_count,
                                "session_id": state.get("session_id"),
                            }
                        },
                    )
                    # Skip this step and move to next
                    step_record = (current_step, "Step skipped due to execution limit")
                    return {
                        "past_steps": [step_record],
                        "plan": remaining_plan,
                        "current_step": "",
                        "step_execution_count": 0,
                        "messages": [],
                    }
            else:
                step_count = 1

            # Calculate current step position and total steps
            current_step_number = len(state.get("past_steps", [])) + 1
            total_steps = len(state.get("past_steps", [])) + len(state["plan"])

            logging.info(
                "⚡ Executing plan step",
                extra={
                    "details": {
                        "step_number": current_step_number,
                        "total_steps": total_steps,
                        "step_text": LoggingUtils.truncate_for_logging(
                            current_step, 300
                        ),
                        "execution_count": step_count,
                        "session_id": state.get("session_id"),
                    }
                },
            )

            # Update session tasks for UI
            session_id = state.get("session_id")
            run_id = state.get("run_id")
            if session_id and run_id:
                self._update_session_tasks(
                    session_id, run_id, current_step=current_step
                )

            # Create execution prompt with context
            context = ""
            if state.get("past_steps"):
                context = "\n".join(
                    [
                        f"Previous step: {step}\nResult: {result}"
                        for step, result in state["past_steps"]
                    ]
                )
                context = f"\nPrevious steps completed:\n{context}\n"

            # Include chat history context if available
            history_section = ""
            if state.get("chat_history_context"):
                history_section = (
                    f"\nPREVIOUS CONVERSATION:\n{state['chat_history_context']}\n"
                )

            execution_prompt = f"""
            ORIGINAL QUESTION: {state['input']}{history_section}

            You are executing this step in a log analysis plan: {current_step}

            {context}

            Use the query_mongodb tool to gather the necessary data for this step.
            Be specific in your queries and provide analysis of the results.
            Focus only on this step - don't try to do multiple steps at once.
            Remember to use the exact identifiers and context from the original question and previous conversation.
            """

            # Log the execution prompt being created
            logging.info(
                "⚡ Step execution prompt created",
                extra={
                    "details": {
                        "prompt_length": len(execution_prompt),
                        "has_context": bool(context),
                        "context_length": len(context) if context else 0,
                        "current_step_text": LoggingUtils.truncate_for_logging(
                            current_step, 200
                        ),
                    }
                },
            )

            # Create message for the agent
            execution_message = HumanMessage(content=execution_prompt)

            # Update state with current step context
            return {
                "messages": [execution_message],
                "current_step": current_step,
                "plan": remaining_plan,
                "step_execution_count": step_count,
            }

        def tools_condition(state: PlanExecuteState) -> str:
            """Route to tools or continue based on agent response."""
            messages = state.get("messages", [])

            # Log workflow routing decision
            logging.info(
                "🔄 Workflow routing evaluation",
                extra={
                    "details": {
                        "messages_count": len(messages),
                        "session_id": state.get("session_id"),
                        "current_step": state.get("current_step", "unknown"),
                    }
                },
            )

            if not messages:
                logging.info("🔄 No messages - routing to continue_execution")
                return "continue_execution"

            last_message = messages[-1]
            if (
                isinstance(last_message, AIMessage)
                and hasattr(last_message, "tool_calls")
                and last_message.tool_calls
            ):
                logging.info(
                    "🔧 Routing to tools - agent requested tool execution",
                    extra={
                        "details": {
                            "tool_calls_count": len(last_message.tool_calls),
                            "tool_names": [
                                tc["name"] for tc in last_message.tool_calls
                            ],
                            "session_id": state.get("session_id"),
                        }
                    },
                )
                return "tools"

            logging.info(
                "🔄 Routing to continue_execution - no tool calls needed",
                extra={
                    "details": {
                        "last_message_type": type(last_message).__name__,
                        "has_content": bool(getattr(last_message, "content", None)),
                        "session_id": state.get("session_id"),
                    }
                },
            )
            return "continue_execution"

        def should_continue(state: PlanExecuteState) -> str:
            """Decide whether to continue executing steps or provide final answer."""
            remaining_steps = len(state.get("plan", []))
            completed_steps = len(state.get("past_steps", []))

            # Log workflow continuation decision
            logging.info(
                "🔄 Workflow continuation evaluation",
                extra={
                    "details": {
                        "remaining_steps": remaining_steps,
                        "completed_steps": completed_steps,
                        "decision": "finish" if remaining_steps == 0 else "execute",
                        "session_id": state.get("session_id"),
                    }
                },
            )

            if not state["plan"]:  # No more steps
                logging.info("🏁 All steps completed - routing to finish")
                return "finish"

            logging.info(f"➡️ {remaining_steps} steps remaining - routing to execute")
            return "execute"

        def process_step_result(state: PlanExecuteState) -> Dict[str, Any]:
            """Process the result of a step execution."""
            messages = state.get("messages", [])
            current_step = state.get("current_step", "")

            # Log step result processing initiation
            logging.info(
                "📝 Processing step result",
                extra={
                    "details": {
                        "messages_count": len(messages),
                        "current_step_text": LoggingUtils.truncate_for_logging(
                            current_step, 200
                        ),
                        "session_id": state.get("session_id"),
                    }
                },
            )

            if messages:
                # Get the last response for this step
                step_result = ""
                for message in reversed(messages):
                    if (
                        isinstance(message, AIMessage)
                        and message.content
                        and not getattr(message, "tool_calls", None)
                    ):
                        step_result = message.content
                        break

                if not step_result:
                    step_result = "Step completed without text response"

                # Record the step execution
                step_record = (current_step, step_result)

                logging.info(
                    "✅ Step execution completed successfully",
                    extra={
                        "details": {
                            "step_text": LoggingUtils.truncate_for_logging(
                                current_step, 200
                            ),
                            "result_length": len(step_result),
                            "result_preview": LoggingUtils.truncate_for_logging(
                                step_result, 300
                            ),
                            "session_id": state.get("session_id"),
                        }
                    },
                )

                # Update session tasks for UI
                session_id = state.get("session_id")
                run_id = state.get("run_id")
                if session_id and run_id:
                    self._update_session_tasks(
                        session_id,
                        run_id,
                        completed_step=current_step,
                        step_result=step_result,
                    )

                return {
                    "past_steps": [step_record],
                    "messages": [],  # Clear messages for next step
                    "current_step": "",  # Reset current step
                    "step_execution_count": 0,  # Reset execution count
                }

            logging.warning(
                "⚠️ No messages found to process for step result",
                extra={
                    "details": {
                        "current_step": LoggingUtils.truncate_for_logging(
                            current_step, 200
                        ),
                        "session_id": state.get("session_id"),
                    }
                },
            )
            return {}

        def finish_step(state: PlanExecuteState) -> Dict[str, Any]:
            """
            Synthesize final answer from all executed steps.

            This replaces the manual final response generation.
            """

            # Log final step initiation
            logging.info(
                "🏁 Final answer synthesis initiated",
                extra={
                    "details": {
                        "completed_steps_count": len(state.get("past_steps", [])),
                        "original_question_length": len(state.get("input", "")),
                        "persona": state.get("persona", "business"),
                        "session_id": state.get("session_id"),
                    }
                },
            )

            # Compile all step results
            steps_summary = "\n\n".join(
                [
                    f"Step: {step}\nResults: {result}"
                    for step, result in state["past_steps"]
                ]
            )

            # Include chat history context in final answer if available
            history_section = ""
            if state.get("chat_history_context"):
                history_section = (
                    f"\n\nPrevious conversation:\n{state['chat_history_context']}\n"
                )

            final_prompt = f"""
            Original question: {state['input']}{history_section}

            You have completed the following analysis steps:
            {steps_summary}

            Now provide a comprehensive final answer that:
            1. Directly answers the original question
            2. Incorporates insights from all completed steps
            3. Ensures that all facts are grounded in response from MongoDB tool
            3. Provides clear, actionable findings
            4. Uses a tone appropriate for the {state.get('persona', 'business')} persona
            5. References previous conversation context if relevant

            Your response should be complete and self-contained.
            """

            # Log final prompt creation
            logging.info(
                "🏁 Final synthesis prompt created",
                extra={
                    "details": {
                        "prompt_length": len(final_prompt),
                        "steps_summary_length": len(steps_summary),
                        "steps_processed": len(state.get("past_steps", [])),
                    }
                },
            )

            # Use Google GenAI client directly
            user_content = types.Content(
                role="user", parts=[types.Part(text=final_prompt)]
            )

            # Get base system prompt
            base_system_prompt = self.prompt_manager.get_langgraph_system_prompt(
                mongodb_db=self.database_manager.database.name,
                collection_name=self.database_manager.summarized_collection_name,
                persona=state.get("persona", "business"),
            )

            # Add memory context to system prompt
            memory_context_section = self._format_memory_context(
                state.get("memory_context", {})
            )
            system_prompt = base_system_prompt + memory_context_section

            # Log the final constructed system prompt
            logging.info(
                "📋 Final system prompt constructed for finish step",
                extra={
                    "details": {
                        "base_prompt_length": len(base_system_prompt),
                        "memory_context_length": len(memory_context_section),
                        "total_prompt_length": len(system_prompt),
                        "has_memory_context": len(memory_context_section) > 0,
                        "full_system_prompt": system_prompt,
                    }
                },
            )

            try:
                # Log final model call
                logging.info(
                    "🏁 Calling model for final answer synthesis",
                    extra={
                        "details": {
                            "model_name": self.config.model_name,
                            "system_prompt_length": len(system_prompt),
                            "final_prompt_length": len(final_prompt),
                        }
                    },
                )

                # Check input size before API call
                input_analysis = self._check_input_size_before_api_call(
                    system_prompt, [user_content], "final_answer"
                )

                # Proceed with API call (even if large - let Gemini handle it)
                response = call_google_genai_with_retry(
                    client=self.client,
                    model=self.config.model_name,
                    contents=[user_content],
                    config=types.GenerateContentConfig(
                        system_instruction=system_prompt
                    ),
                )

                # Log API usage and cost for final answer call
                session_id = state.get("session_id")
                run_id = state.get("run_id")
                if response:
                    self._log_api_usage_and_cost(
                        response, "final_answer", session_id, run_id
                    )

                # Log successful final response
                logging.info(
                    "🏁 Final answer model call completed",
                    extra={
                        "details": {
                            "has_response": response is not None,
                            "has_candidates": hasattr(response, "candidates")
                            and bool(response.candidates),
                        }
                    },
                )
            except Exception as e:
                logging.exception(
                    "🏁 Final answer model call failed",
                    extra={
                        "details": {
                            "error_message": str(e),
                            "error_type": type(e).__name__,
                            "session_id": state.get("session_id"),
                        }
                    },
                )
                raise

            # Extract final answer from the response with proper error handling
            final_answer = ""
            if response and hasattr(response, "text") and response.text:
                final_answer = response.text
            elif response and hasattr(response, "candidates") and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, "content") and candidate.content:
                    if hasattr(candidate.content, "parts") and candidate.content.parts:
                        for part in candidate.content.parts:
                            if hasattr(part, "text") and part.text:
                                final_answer += part.text

            if not final_answer:
                final_answer = "Unable to generate final answer from model response"

            # Log final answer generation completion
            logging.info(
                "🏁 Final answer generation completed",
                extra={
                    "details": {
                        "final_answer_length": len(final_answer),
                        "steps_processed": len(state["past_steps"]),
                        "answer_preview": LoggingUtils.truncate_for_logging(
                            final_answer, 300
                        ),
                        "session_id": state.get("session_id"),
                        "generation_successful": len(final_answer) > 10,
                    }
                },
            )

            return {"response": final_answer}

        def query_classifier(state: PlanExecuteState) -> Dict[str, Any]:
            """
            Fast LLM classification with combined response generation.
            Routes queries to appropriate execution paths.
            """

            logging.info(
                "🧠 Query classification initiated",
                extra={
                    "details": {
                        "query_length": len(state.get("input", "")),
                        "session_id": state.get("session_id"),
                    }
                },
            )

            # Include chat history context for better classification and response generation
            context_section = ""
            if state.get("chat_history_context"):
                context_section = f"""
            Previous conversation context:
            {state.get('chat_history_context')}
            """

            classification_prompt = f"""
            You are a log intelligence assistant. Analyze this user query:{context_section}

            Current Query: "{state.get('input', '')}"

            First, determine if this query requires database access to log data:
            - Conversational queries ("Hello", "Hi", "Help", "What can you do?") → NO database needed
            - Capability questions ("How do you work?", "What are your features?") → NO database needed
            - Follow-up questions that can be answered from previous context → NO database needed if context sufficient
            - Log analysis requests ("Show recent errors", "Analyze approval rates") → YES database needed
            - Specific application queries ("What happened to app ABC123") → YES database needed
            - Complex analytical queries ("Compare metrics", "Find patterns") → YES database needed
            - Follow-up questions requiring new data ("What about other applications?") → YES database needed

            If NO database needed, provide a helpful direct response using available context and your capabilities as a log intelligence assistant.
            If YES database needed, leave the direct_response field empty (will use Plan-Execute workflow).

            Respond with JSON only:
            """

            # Log the full classification prompt and execution trace availability
            logging.info(
                "🧠 Query classifier prompt constructed",
                extra={
                    "details": {
                        "prompt": classification_prompt,
                        "prompt_length": len(classification_prompt),
                        "has_chat_history": bool(state.get("chat_history_context")),
                        "chat_history_length": len(
                            state.get("chat_history_context", "")
                        ),
                        "chat_history_content": state.get("chat_history_context", ""),
                        "user_query": state.get("input", ""),
                        "session_id": state.get("session_id"),
                        "has_execution_traces_in_state": any(
                            "execution_trace" in str(msg)
                            for msg in state.get("messages", [])
                        ),
                        "state_keys": list(state.keys()),
                        "messages_count": len(state.get("messages", [])),
                    }
                },
            )

            try:
                response = call_google_genai_with_retry(
                    client=self.client,
                    model=self.config.lite_model,  # Use fast model for classification
                    contents=[classification_prompt],
                    config=types.GenerateContentConfig(
                        response_mime_type="application/json",
                        response_schema=types.Schema(
                            type=types.Type.OBJECT,
                            properties={
                                "requires_db_query": types.Schema(
                                    type=types.Type.BOOLEAN,
                                    description="True if query needs database access, False for conversational",
                                ),
                                "direct_response": types.Schema(
                                    type=types.Type.STRING,
                                    description="Complete response if requires_db_query is False, empty otherwise",
                                ),
                            },
                            required=[
                                "requires_db_query",
                                "direct_response",
                            ],
                        ),
                    ),
                )

                # Log API usage and cost for query classifier
                session_id = state.get("session_id")
                run_id = state.get("run_id")
                if response:
                    self._log_api_usage_and_cost(
                        response, "query_classifier", session_id, run_id
                    )

                import json

                # Log the raw response for debugging
                logging.info(
                    f"🧠 Raw classifier response: {response.text}",
                    extra={
                        "details": {
                            "response_length": len(response.text),
                            "session_id": state.get("session_id"),
                        }
                    },
                )

                try:
                    result = json.loads(response.text)
                except json.JSONDecodeError as json_error:
                    logging.exception(
                        f"❌ JSON parsing failed: {str(json_error)}",
                        extra={
                            "details": {
                                "raw_response": response.text,
                                "json_error": str(json_error),
                                "session_id": state.get("session_id"),
                            }
                        },
                    )
                    # Fallback: provide default response
                    result = {
                        "requires_db_query": False,
                        "direct_response": "Hello! I'm a log intelligence assistant. I can help you analyze application logs. What would you like to know?",
                    }

                logging.info(
                    "🧠 Query classification completed",
                    extra={
                        "details": {
                            "requires_db_query": result["requires_db_query"],
                            "session_id": state.get("session_id"),
                        }
                    },
                )

                if not result["requires_db_query"]:
                    # Complete the workflow immediately with direct response
                    logging.info(
                        "🚀 Direct response provided - workflow complete",
                        extra={
                            "details": {
                                "response_length": len(result["direct_response"]),
                                "session_id": state.get("session_id"),
                            }
                        },
                    )

                    return {
                        "requires_db_query": False,
                        "response": result["direct_response"],
                        "route": "complete",
                    }
                else:
                    # Continue to Plan-Execute workflow
                    logging.info(
                        "📋 Routing to Plan-Execute workflow",
                        extra={
                            "details": {
                                "session_id": state.get("session_id"),
                            }
                        },
                    )

                    return {
                        "requires_db_query": True,
                        "route": "plan_execute",
                    }

            except Exception as e:
                logging.exception(
                    f"❌ Query classification failed: {str(e)}",
                    extra={
                        "details": {
                            "error": str(e),
                            "error_type": type(e).__name__,
                            "session_id": state.get("session_id"),
                        }
                    },
                )

                # Fail fast with user-friendly error response
                return {
                    "requires_db_query": False,
                    "response": "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
                    "route": "complete",
                }

        # Build the optimized workflow graph with query classification
        workflow = StateGraph(PlanExecuteState)

        # Add classifier node (new)
        workflow.add_node("query_classifier", query_classifier)

        # Add existing nodes for complex path
        workflow.add_node("consult_memory", consult_memory)
        workflow.add_node("plan", plan_step)
        workflow.add_node("execute", execute_step)
        workflow.add_node("agent", call_agent)
        workflow.add_node("tools", debug_tool_node)
        workflow.add_node("process_result", process_step_result)
        workflow.add_node("finish", finish_step)

        # Route from START to classifier first
        workflow.add_edge(START, "query_classifier")

        # Route based on classification result
        workflow.add_conditional_edges(
            "query_classifier",
            lambda state: state.get("route", "plan_execute"),
            {
                "complete": END,  # Direct response already provided in classifier
                "plan_execute": "consult_memory",
            },
        )

        # Complex path flow (existing logic)
        workflow.add_edge("consult_memory", "plan")
        workflow.add_edge("plan", "execute")
        workflow.add_edge("execute", "agent")

        # Conditional routing from agent
        workflow.add_conditional_edges(
            "agent",
            tools_condition,
            {"tools": "tools", "continue_execution": "process_result"},
        )

        # After tools, continue with processing the result
        workflow.add_edge("tools", "process_result")

        # After processing result, check if more steps
        workflow.add_conditional_edges(
            "process_result",
            should_continue,
            {"execute": "execute", "finish": "finish"},
        )

        workflow.add_edge("finish", END)

        return workflow.compile()

    def _handle_learn_command(self, session_id: str, user_id: str = None) -> str:
        """
        Handle the /learn command to extract learnings from session conversation.
        Only allowed once per session.
        """
        try:
            # Check if session has already been learned from
            if self._session_already_learned(session_id):
                return "❌ Learning has already been completed for this session. Each session can only be learned from once."

            # Get complete conversation history
            conversation_history = self._get_session_conversation_history(session_id)

            if not conversation_history or len(conversation_history) < 2:
                return "❌ Not enough conversation history to learn from. Have at least one complete interaction first."

            # Extract memory contexts used throughout session
            memory_contexts_used = self._extract_memory_contexts_from_conversation(
                conversation_history
            )

            # Analyze conversation for knowledge gaps
            learning_results = self._analyze_conversation_for_learnings(
                conversation_history, memory_contexts_used, session_id, user_id
            )

            # Mark session as learned from
            self._mark_session_learned(session_id)

            # Format results for user
            return self._format_learning_results(learning_results)

        except Exception as e:
            logging.exception(f"Error in learn command for session {session_id}: {e}")
            return f"❌ Error occurred while learning from session: {str(e)}"

    def _session_already_learned(self, session_id: str) -> bool:
        """Check if session has already been learned from."""
        try:
            session_data = self.database_manager.get_chat_session(session_id)
            return session_data and session_data.get("learned_from", False)
        except Exception as e:
            logging.exception(f"Error checking if session learned: {e}")
            return False

    def _get_session_conversation_history(self, session_id: str) -> List[dict]:
        """Get complete conversation history for the session."""
        try:
            # Get complete session data (same as UI uses)
            session_data = self.database_manager.get_chat_session(session_id)

            if not session_data:
                logging.warning(f"No session data found for {session_id}")
                return []

            # Extract messages from session data - check both 'messages' and 'history' fields
            messages = session_data.get("history", session_data.get("messages", []))
            if not messages:
                logging.warning(f"No messages found in session {session_id}")
                return []

            conversation = []
            for msg in messages:
                if msg.get("type") == "user":
                    conversation.append(
                        {
                            "type": "user_message",
                            "content": msg.get("content", ""),
                            "timestamp": msg.get("timestamp"),
                            "persona": session_data.get("persona", "business"),
                        }
                    )
                elif msg.get("type") == "bot":
                    conversation.append(
                        {
                            "type": "agent_response",
                            "content": msg.get("content", ""),
                            "timestamp": msg.get("timestamp"),
                            "memory_context_used": msg.get("memory_context", {}),
                            "execution_trace": msg.get(
                                "execution_trace", []
                            ),  # Include execution trace
                        }
                    )

            return sorted(conversation, key=lambda x: x.get("timestamp", ""))

        except Exception as e:
            logging.exception(
                f"Error getting conversation history for {session_id}: {e}"
            )
            return []

    def _extract_memory_contexts_from_conversation(
        self, conversation_history: List[dict]
    ) -> List[dict]:
        """Extract all memory contexts used throughout the conversation."""
        memory_contexts = []

        for message in conversation_history:
            if message.get("type") == "agent_response":
                memory_context = message.get("memory_context_used", {})
                if memory_context:
                    memory_contexts.append(memory_context)

        return memory_contexts

    def _analyze_conversation_for_learnings(
        self,
        conversation_history: List[dict],
        memory_contexts_used: List[dict],
        session_id: str,
        user_id: str = None,
    ) -> dict:
        """Use LLM to analyze conversation and identify knowledge gaps."""
        from src.memory import MemoryManager

        try:
            # Log what we have in conversation history
            logging.info(
                "📚 Analyzing conversation for learnings",
                extra={
                    "details": {
                        "session_id": session_id,
                        "conversation_length": len(conversation_history),
                        "has_execution_traces": any(
                            msg.get("execution_trace") for msg in conversation_history
                        ),
                        "execution_trace_counts": [
                            len(msg.get("execution_trace", []))
                            for msg in conversation_history
                            if msg.get("type") == "agent_response"
                        ],
                    }
                },
            )

            # Format conversation for LLM analysis
            formatted_conversation = self._format_conversation_for_analysis(
                conversation_history
            )
            formatted_memory_contexts = self._format_memory_contexts_for_analysis(
                memory_contexts_used
            )

            # Log the formatted conversation to see if execution traces are included
            logging.info(
                "📚 Formatted conversation for analysis",
                extra={
                    "details": {
                        "formatted_length": len(formatted_conversation),
                        "includes_execution_trace": "EXECUTION TRACE:"
                        in formatted_conversation,
                        "trace_count": formatted_conversation.count("EXECUTION TRACE:"),
                        "preview": (
                            formatted_conversation[:500] + "..."
                            if len(formatted_conversation) > 500
                            else formatted_conversation
                        ),
                    }
                },
            )

            # Get analysis prompt from PromptManager and format it
            prompt_manager = PromptManager()
            analysis_prompt = prompt_manager.get_analysis_prompt().format(
                formatted_memory_contexts=formatted_memory_contexts,
                formatted_conversation=formatted_conversation,
            )

            # Define response schema matching teach_* method parameters
            response_schema = types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "domain_workflow": types.Schema(
                        type=types.Type.ARRAY,
                        items=types.Schema(
                            type=types.Type.OBJECT,
                            properties={
                                "question": types.Schema(
                                    type=types.Type.STRING,
                                    description="The canonical question this workflow answers",
                                ),
                                "logic": types.Schema(
                                    type=types.Type.OBJECT,
                                    properties={
                                        "steps": types.Schema(
                                            type=types.Type.ARRAY,
                                            items=types.Schema(type=types.Type.STRING),
                                            description="Step-by-step analytical procedure",
                                        ),
                                        "strategy": types.Schema(
                                            type=types.Type.STRING,
                                            description="Overall strategy or approach",
                                        ),
                                    },
                                    required=["steps", "strategy"],
                                ),
                                "example_phrasings": types.Schema(
                                    type=types.Type.ARRAY,
                                    items=types.Schema(type=types.Type.STRING),
                                    description="Alternative ways users might ask this question",
                                ),
                            },
                            required=["question", "logic", "example_phrasings"],
                        ),
                    ),
                    "field_knowledge": types.Schema(
                        type=types.Type.ARRAY,
                        items=types.Schema(
                            type=types.Type.OBJECT,
                            properties={
                                "title": types.Schema(
                                    type=types.Type.STRING,
                                    description="Name of the field or concept",
                                ),
                                "body": types.Schema(
                                    type=types.Type.STRING,
                                    description="Detailed explanation of the field",
                                ),
                                "field_type": types.Schema(
                                    type=types.Type.STRING,
                                    description="Type of knowledge (e.g., 'log_schema', 'query_syntax', 'data_processing')",
                                ),
                                "aliases": types.Schema(
                                    type=types.Type.ARRAY,
                                    items=types.Schema(type=types.Type.STRING),
                                    description="Alternative names or terms for this field",
                                ),
                            },
                            required=["title", "body", "field_type", "aliases"],
                        ),
                    ),
                    "translation_mapping": types.Schema(
                        type=types.Type.ARRAY,
                        items=types.Schema(
                            type=types.Type.OBJECT,
                            properties={
                                "business_term": types.Schema(
                                    type=types.Type.STRING,
                                    description="Business language term used by users",
                                ),
                                "technical_mapping": types.Schema(
                                    type=types.Type.STRING,
                                    description="Technical implementation or field mapping",
                                ),
                                "aliases": types.Schema(
                                    type=types.Type.ARRAY,
                                    items=types.Schema(type=types.Type.STRING),
                                    description="Alternative phrasings of the business term",
                                ),
                            },
                            required=["business_term", "technical_mapping", "aliases"],
                        ),
                    ),
                    "agent_guidance": types.Schema(
                        type=types.Type.ARRAY,
                        items=types.Schema(
                            type=types.Type.OBJECT,
                            properties={
                                "title": types.Schema(
                                    type=types.Type.STRING,
                                    description="Title of the guidance rule or strategy",
                                ),
                                "body": types.Schema(
                                    type=types.Type.STRING,
                                    description="Detailed guidance content and decision-making logic",
                                ),
                            },
                            required=["title", "body"],
                        ),
                    ),
                    "summary": types.Schema(
                        type=types.Type.STRING,
                        description="Brief summary of overall learning session findings",
                    ),
                },
                required=[
                    "domain_workflow",
                    "field_knowledge",
                    "translation_mapping",
                    "agent_guidance",
                    "summary",
                ],
            )

            # Use existing GenAI client for analysis with structured output
            system_prompt = "You are an expert at analyzing conversations to identify knowledge gaps and learning opportunities."
            user_content = types.Content(
                role="user", parts=[types.Part(text=analysis_prompt)]
            )

            response = call_google_genai_with_retry(
                client=self.client,
                model=self.config.model_name,
                contents=[user_content],
                config=types.GenerateContentConfig(
                    system_instruction=system_prompt,
                    response_schema=response_schema,
                    response_mime_type="application/json",
                ),
            )

            # Parse structured response - should be valid JSON now
            import json

            learnings = json.loads(response.text)

            # Process each category of learnings
            memory_manager = MemoryManager(self.config)
            results = {
                "new_memories_created": [],
                "feedback_added_to": [],
                "learnings_processed": 0,
                "analysis_time": time.time(),
            }

            for category, items in learnings.items():
                if items and isinstance(items, list):
                    category_results = self._process_learnings_for_category(
                        memory_manager, category, items, session_id
                    )
                    results["new_memories_created"].extend(
                        category_results.get("new_memories_created", [])
                    )
                    results["feedback_added_to"].extend(
                        category_results.get("feedback_added_to", [])
                    )
                    results["learnings_processed"] += category_results.get(
                        "learnings_processed", 0
                    )

            # Log learning results
            logging.info(
                "📚 Session learning completed",
                extra={
                    "details": {
                        "session_id": session_id,
                        "new_memories": len(results["new_memories_created"]),
                        "feedback_added": len(results["feedback_added_to"]),
                        "learnings_processed": results["learnings_processed"],
                    }
                },
            )

            return results

        except Exception as e:
            logging.exception(f"Error analyzing conversation for learnings: {e}")
            return {
                "error": str(e),
                "new_memories_created": [],
                "feedback_added_to": [],
                "learnings_processed": 0,
            }

    def _process_learnings_for_category(
        self,
        memory_manager: MemoryManager,
        category: str,
        learnings: List[dict],
        session_id: str,
    ) -> dict:
        """Process learnings for a specific memory category."""
        results = {
            "new_memories_created": [],
            "feedback_added_to": [],
            "learnings_processed": len(learnings),
        }

        for learning in learnings:
            try:
                # Build search query from learning data
                search_query = self._build_search_query_from_learning(
                    learning, category
                )

                # Only search if we have a non-empty query
                if search_query:
                    similar_memories = memory_manager.search(
                        query=search_query, type_filter=category, top_k=3
                    )
                else:
                    similar_memories = []

                if similar_memories and similar_memories[0].get("similarity", 0) > 0.8:
                    # Very similar memory exists - add feedback
                    memory_id = similar_memories[0]["memory_id"]
                    feedback_notes = (
                        f"Knowledge gap identified in conversation analysis. "
                        f"Learning context: {learning.get('would_have_helped', 'Additional context identified')}"
                    )
                    memory_manager.add_feedback(
                        memory_id=memory_id,
                        user_id=session_id,  # Use session_id as user identifier
                        rating="knowledge_gap",
                        notes=feedback_notes,
                    )
                    results["feedback_added_to"].append(memory_id)

                else:
                    # No similar memory - create new one using appropriate teach method
                    memory_id = self._create_memory_using_teach_method(
                        category, learning, memory_manager, session_id
                    )
                    if memory_id:
                        results["new_memories_created"].append(memory_id)

            except Exception as e:
                logging.warning(f"Error processing learning {learning}: {e}")
                continue

        return results

    def _create_memory_using_teach_method(
        self, category: str, learning: dict, memory_manager, session_id: str
    ) -> str:
        """Create memory using appropriate teach method with aligned parameters."""
        try:
            # Use session_id as created_by for better tracking
            created_by = session_id

            if category == "domain_workflow":
                return memory_manager.teach_domain_workflow(
                    question=learning.get("question", ""),
                    logic=learning.get("logic", {}),
                    example_phrasings=learning.get("example_phrasings", []),
                    created_by=created_by,
                )
            elif category == "field_knowledge":
                return memory_manager.teach_field_knowledge(
                    title=learning.get("title", ""),
                    body=learning.get("body", ""),
                    field_type=learning.get("field_type", "conversation_learned"),
                    aliases=learning.get("aliases", []),
                    created_by=created_by,
                )
            elif category == "translation_mapping":
                return memory_manager.teach_translation_mapping(
                    business_term=learning.get("business_term", ""),
                    technical_mapping=learning.get("technical_mapping", ""),
                    aliases=learning.get("aliases", []),
                    created_by=created_by,
                )
            elif category == "agent_guidance":
                return memory_manager.teach_agent_guidance(
                    title=learning.get("title", ""),
                    body=learning.get("body", ""),
                    created_by=created_by,
                )

            logging.warning(f"Unknown memory category: {category}")
            return None

        except Exception as e:
            logging.exception(f"Error creating memory from learning: {e}")
            return None

    def _build_search_query_from_learning(self, learning: dict, category: str) -> str:
        """Build search query from learning data based on category."""
        query_parts = []

        if category == "domain_workflow":
            # Use question and strategy for search
            if learning.get("question"):
                query_parts.append(learning["question"])
            if learning.get("logic", {}).get("strategy"):
                query_parts.append(learning["logic"]["strategy"])

        elif category == "field_knowledge":
            # Use title and body for search
            if learning.get("title"):
                query_parts.append(learning["title"])
            if learning.get("body"):
                query_parts.append(learning["body"])

        elif category == "translation_mapping":
            # Use business term and technical mapping
            if learning.get("business_term"):
                query_parts.append(learning["business_term"])
            if learning.get("technical_mapping"):
                query_parts.append(learning["technical_mapping"])

        elif category == "agent_guidance":
            # Use title and body for search
            if learning.get("title"):
                query_parts.append(learning["title"])
            if learning.get("body"):
                query_parts.append(learning["body"])

        # Join non-empty parts
        search_query = " ".join(query_parts).strip()
        return search_query if search_query else None

    def _extract_steps_from_description(self, description: str) -> List[str]:
        """Extract step-by-step instructions from description."""
        # Simple implementation - split on numbered items or sentences
        if any(char in description for char in ["1.", "2.", "3."]):
            steps = []
            for line in description.split("\n"):
                line = line.strip()
                if line and any(line.startswith(f"{i}.") for i in range(1, 10)):
                    steps.append(line)
            return steps if steps else [description]
        else:
            # Fallback to single step
            return [description]

    def _format_conversation_for_analysis(
        self, conversation_history: List[dict]
    ) -> str:
        """Format conversation history for LLM analysis, including execution traces."""
        formatted = []
        for msg in conversation_history:
            if msg.get("type") == "user_message":
                formatted.append(f"USER: {msg.get('content', '')}")
            elif msg.get("type") == "agent_response":
                # Add agent response
                formatted.append(f"AGENT RESPONSE: {msg.get('content', '')}")

                # Add detailed execution trace if available
                execution_trace = msg.get("execution_trace", [])
                if execution_trace:
                    formatted.append("EXECUTION TRACE:")

                    step_counter = 0
                    for trace_msg in execution_trace:
                        msg_type = trace_msg.get("type", "")

                        if msg_type == "HumanMessage":
                            step_counter += 1
                            content = trace_msg.get("content", "")
                            # Extract step description from the prompt
                            if "executing this step in a log analysis plan:" in content:
                                step_desc = (
                                    content.split(
                                        "executing this step in a log analysis plan:"
                                    )[1]
                                    .split("\n")[0]
                                    .strip()
                                )
                                formatted.append(f"  STEP {step_counter}: {step_desc}")

                        elif msg_type == "AIMessage":
                            tool_calls = trace_msg.get("tool_calls", [])
                            if tool_calls:
                                for tool_call in tool_calls:
                                    tool_name = tool_call.get("name", "")
                                    tool_args = tool_call.get("args", {})
                                    formatted.append(f"    🔧 TOOL CALL: {tool_name}")
                                    formatted.append(
                                        f"       Args: {str(tool_args)[:200]}..."
                                    )
                            else:
                                content = trace_msg.get("content", "")
                                if content and content.strip():
                                    formatted.append(
                                        f"    🤖 REASONING: {content[:200]}..."
                                    )

                        elif msg_type == "ToolMessage":
                            content = trace_msg.get("content", "")
                            if (
                                "failed" in content.lower()
                                or "error" in content.lower()
                            ):
                                formatted.append(
                                    f"    ❌ TOOL ERROR: {content[:200]}..."
                                )
                            else:
                                formatted.append(
                                    f"    ✅ TOOL RESULT: {content[:200]}..."
                                )

                    formatted.append("END EXECUTION TRACE")

        return "\n\n".join(formatted)

    def _format_memory_contexts_for_analysis(self, memory_contexts: List[dict]) -> str:
        """Format memory contexts for LLM analysis."""
        if not memory_contexts:
            return "No memory contexts were used in this session."

        formatted = []
        for i, context in enumerate(memory_contexts, 1):
            formatted.append(f"Memory Context {i}:")
            for memory_type, memories in context.items():
                if memories:
                    formatted.append(f"  {memory_type}: {len(memories)} memories used")

        return "\n".join(formatted)

    def _mark_session_learned(self, session_id: str):
        """Mark session as learned from to prevent duplicate learning."""
        try:
            # Update chat session directly using existing pattern
            result = self.database_manager.database.chat_sessions.update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "learned_from": True,
                        "learned_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    }
                },
            )
            if result.modified_count > 0:
                logging.info(
                    f"Successfully marked session {session_id} as learned from"
                )
            else:
                logging.warning(
                    f"Failed to mark session {session_id} as learned - session not found or already marked"
                )
        except Exception as e:
            logging.exception(f"Error marking session as learned: {e}")

    def _format_learning_results(self, results: dict) -> str:
        """Format learning results for user display."""
        if results.get("error"):
            return f"❌ Error during learning: {results['error']}"

        new_memories = len(results.get("new_memories_created", []))
        feedback_added = len(results.get("feedback_added_to", []))
        learnings_processed = results.get("learnings_processed", 0)

        if new_memories == 0 and feedback_added == 0:
            return "✅ Learning completed! No new knowledge gaps were identified. The existing memory system appears to have covered this conversation well."

        summary = f"✅ Learning completed! Processed {learnings_processed} potential learnings:\n"

        if new_memories > 0:
            summary += f"📝 Created {new_memories} new memories\n"

        if feedback_added > 0:
            summary += f"💬 Added feedback to {feedback_added} existing memories\n"

        summary += "\nThe agent will use these learnings to improve future responses!"

        return summary

    def run(
        self,
        question: str,
        session_id: str,
        persona: str = "business",
        user_id: str = None,
    ) -> str:
        """
        Execute the agent's Plan-and-Execute workflow.

        This replaces the complex iteration loop with LangGraph's built-in execution.
        """
        # Check for special /learn command
        if question.strip() == "/learn":
            return self._handle_learn_command(session_id, user_id)

        run_start_time = time.time()
        run_id = f"run-{uuid.uuid4()}"

        # Create run entry immediately when query is received
        try:
            self.database_manager.create_run_entry(session_id, run_id, persona)
        except Exception as e:
            logging.exception(f"Failed to create run entry: {e}")

        # Set up MongoDB logging
        mongodb_handler = None
        try:
            mongodb_handler = MongoDBLogHandler.get_session_handler(session_id, run_id)
            logging.getLogger().addHandler(mongodb_handler)
        except Exception as e:
            logging.exception(f"Could not set up MongoDB logging handler: {e}")

        # Store user message and retrieve chat history
        chat_history_context = ""
        try:
            existing_session = self.database_manager.get_chat_session(session_id)
            if not existing_session:
                self.database_manager.create_chat_session(
                    session_id, persona, user_id=user_id
                )
            else:
                # Extract previous conversation context for the agent
                if existing_session.get("history"):
                    recent_history = existing_session["history"]

                    # Log execution trace availability in recent history
                    execution_trace_info = []
                    for i, msg in enumerate(recent_history):
                        has_trace = bool(msg.get("execution_trace"))
                        trace_length = len(msg.get("execution_trace", []))
                        execution_trace_info.append(
                            {
                                "message_index": i,
                                "type": msg["type"],
                                "has_execution_trace": has_trace,
                                "trace_length": trace_length,
                            }
                        )

                    history_parts = []
                    for msg in recent_history:
                        role = "User" if msg["type"] == "user" else "Assistant"
                        content = (
                            msg["content"][:500] + "..."
                            if len(msg["content"]) > 500
                            else msg["content"]
                        )  # Truncate long messages

                        # Add execution trace details for assistant messages
                        if msg["type"] == "bot" and msg.get("execution_trace"):
                            execution_trace = msg.get("execution_trace", [])
                            if execution_trace and len(execution_trace) > 0:
                                # Build detailed execution trace showing internal workings
                                trace_parts = []
                                trace_parts.append(
                                    "\n\n[INTERNAL EXECUTION TRACE START]"
                                )

                                for i, trace_msg in enumerate(execution_trace):
                                    msg_type = trace_msg.get("type")
                                    msg_content = trace_msg.get("content", "")
                                    tool_calls = trace_msg.get("tool_calls", [])

                                    if msg_type == "HumanMessage":
                                        # Agent instructions
                                        content_preview = (
                                            msg_content[:500] + "..."
                                            if len(msg_content) > 500
                                            else msg_content
                                        )
                                        content_preview = content_preview.replace(
                                            "\n", " "
                                        ).strip()
                                        trace_parts.append(
                                            f"→ Agent: {content_preview}"
                                        )

                                    elif msg_type == "AIMessage":
                                        # LLM response
                                        if tool_calls:
                                            # LLM decided to use tools
                                            for call in tool_calls:
                                                tool_name = call.get(
                                                    "name", "unknown_tool"
                                                )
                                                tool_args = call.get("args", {})
                                                # Show full args
                                                import json

                                                args_str = json.dumps(
                                                    tool_args, indent=2
                                                )
                                                trace_parts.append(
                                                    f"→ LLM: Calling {tool_name} with args:\n{args_str}"
                                                )
                                        elif msg_content:
                                            # LLM provided direct response - show full content
                                            trace_parts.append(f"→ LLM: {msg_content}")

                                    elif msg_type == "ToolMessage":
                                        # Tool execution result - show full content
                                        content_str = str(msg_content)
                                        content_preview = (
                                            content_str[:500] + "..."
                                            if len(content_str) > 500
                                            else content_str
                                        )
                                        trace_parts.append(
                                            f"→ Tool Result:\n{content_preview}"
                                        )

                                if (
                                    len(trace_parts) > 1
                                ):  # Has more than just the header
                                    trace_parts.append(
                                        "[INTERNAL EXECUTION TRACE END]\n"
                                    )
                                    execution_text = "\n\t".join(trace_parts)
                                    content += execution_text
                                else:
                                    logging.info(
                                        "🔧 No meaningful execution trace found"
                                    )
                            else:
                                logging.info("🔧 Execution trace is empty")
                        elif msg["type"] == "bot":
                            logging.info("🔧 Bot message has no execution_trace")
                        history_parts.append(f"{role}: {content}")
                    chat_history_context = "\n".join(history_parts)

            self.database_manager.store_user_message(
                session_id, question, user_id=user_id, run_id=run_id
            )
        except Exception as e:
            logging.exception(f"Failed to store user message to MongoDB: {e}")

        # Initialize state
        initial_state = {
            "input": question,
            "messages": [],
            "plan": [],
            "past_steps": [],
            "response": "",
            "session_id": session_id,
            "run_id": run_id,  # Add run_id to state for task isolation
            "persona": persona,
            "current_step": "",
            "step_execution_count": 0,
            "mongodb_results": {},
            "chat_history_context": chat_history_context,  # Add chat history to state
        }

        logging.info(
            "🚀 Starting LangGraph Plan-and-Execute workflow",
            extra={
                "details": {
                    "question": LoggingUtils.truncate_for_logging(question, 1000),
                    "session_id": session_id,
                    "persona": persona,
                    "run_id": run_id,
                }
            },
        )

        try:
            # Execute the workflow with recursion limit protection
            final_state = self.workflow.invoke(
                initial_state, config={"recursion_limit": 25}  # Prevent infinite loops
            )
            final_answer = final_state.get("response", "Unable to generate response")
            workflow_success = True

        except Exception as e:
            logging.exception(f"Workflow execution failed: {e}")
            # More specific error handling for common issues
            if "recursion" in str(e).lower() or "maximum" in str(e).lower():
                final_answer = "The analysis workflow encountered a recursion limit. Please try rephrasing your question or break it into smaller parts."
            elif "timeout" in str(e).lower():
                final_answer = "The analysis took too long to complete. Please try a more specific query."
            else:
                final_answer = f"I encountered an error while processing your request: {str(e)[:200]}. Please try again or contact support."
            final_state = {}
            workflow_success = False

        # Log completion with cost summary
        total_run_time = (time.time() - run_start_time) * 1000
        run_cost_summary = self.get_run_cost_summary(run_id)

        logging.info(
            "✅ LangGraph workflow completed",
            extra={
                "details": {
                    "session_id": session_id,
                    "total_runtime_ms": total_run_time,
                    "final_answer_length": len(final_answer),
                    "steps_executed": len(final_state.get("past_steps", [])),
                    "workflow_success": workflow_success,
                    "run_id": run_id,
                    "total_estimated_cost_usd": round(
                        run_cost_summary.get("total_cost", 0), 6
                    ),
                    "total_api_calls": run_cost_summary.get("api_calls", 0),
                    "total_input_tokens": run_cost_summary.get("total_input_tokens", 0),
                    "total_output_tokens": run_cost_summary.get(
                        "total_output_tokens", 0
                    ),
                }
            },
        )

        # Store bot response with execution trace
        try:
            # Capture raw execution trace from LangGraph state
            execution_trace = []
            for msg in final_state.get("messages", []):
                try:
                    msg_data = {
                        "type": type(msg).__name__,
                        "content": (
                            str(msg.content)
                            if hasattr(msg, "content") and msg.content
                            else None
                        ),
                    }

                    if hasattr(msg, "tool_calls") and msg.tool_calls:
                        msg_data["tool_calls"] = [
                            {
                                "name": tc.get("name", ""),
                                "id": tc.get("id", ""),
                                "args": tc.get("args", {}),
                            }
                            for tc in msg.tool_calls
                        ]

                    if hasattr(msg, "tool_call_id"):
                        msg_data["tool_call_id"] = msg.tool_call_id

                    execution_trace.append(msg_data)
                except Exception as e:
                    logging.warning(f"Error serializing message: {e}")
                    continue

            run_metadata = {
                "total_runtime_ms": total_run_time,
                "steps_executed": len(final_state.get("past_steps", [])),
                "persona": persona,
                "run_id": run_id,
                "workflow_type": "plan_and_execute",
                "workflow_success": workflow_success,
                "total_estimated_cost_usd": round(
                    run_cost_summary.get("total_cost", 0), 6
                ),
                "total_api_calls": run_cost_summary.get("api_calls", 0),
                "total_input_tokens": run_cost_summary.get("total_input_tokens", 0),
                "total_output_tokens": run_cost_summary.get("total_output_tokens", 0),
                "execution_trace": execution_trace,  # Add full execution trace to both bot message and run metadata
            }

            self.database_manager.store_bot_message(
                session_id=session_id,
                content=final_answer,
                run_id=run_id,
                metadata=run_metadata,
            )

            self.database_manager.update_session_completion(
                session_id=session_id, metadata=run_metadata
            )

        except Exception as e:
            logging.exception(f"Failed to store bot response to MongoDB: {e}")

        # Mark run as completed
        try:
            self._mark_run_completed(session_id, run_id, workflow_success)
        except Exception as e:
            logging.exception(f"Failed to mark run as completed: {e}")

        # Clean up logging
        if mongodb_handler:
            try:
                logging.getLogger().removeHandler(mongodb_handler)
                mongodb_handler.close()
            except Exception as e:
                logging.exception(f"Could not clean up MongoDB logging handler: {e}")

        # Return answer with metadata
        cost_summary = self.get_run_cost_summary(run_id)

        # Debug logging for cost tracking
        logging.info(f"Cost summary for run_id {run_id}: {cost_summary}")

        return {
            "answer": final_answer,
            "metadata": {
                "run_id": run_id,
                "total_cost_usd": round(cost_summary.get("total_cost", 0), 6),
                "total_input_tokens": cost_summary.get("total_input_tokens", 0),
                "total_output_tokens": cost_summary.get("total_output_tokens", 0),
                "total_tokens": cost_summary.get("total_input_tokens", 0)
                + cost_summary.get("total_output_tokens", 0),
                "api_calls": cost_summary.get("api_calls", 0),
                "execution_time_ms": total_run_time,
                # Add more metadata fields here as needed
            },
        }


# Entry point for testing
if __name__ == "__main__":
    setup_logging()
    parser = argparse.ArgumentParser(description="LangGraph Log Intelligence Agent")
    parser.add_argument("question", type=str, help="Your question for the agent")
    parser.add_argument(
        "--persona",
        type=str,
        choices=["business", "developer", "support"],
        default="business",
        help="Persona for the agent's response",
    )
    parser.add_argument(
        "--user-id", type=str, help="Optional: User ID for session tracking"
    )
    parser.add_argument(
        "--session-id",
        type=str,
        help="Optional: Session ID for conversation continuity",
    )
    args = parser.parse_args()

    # Use provided session ID or generate new one
    session_id = args.session_id or f"session-{uuid.uuid4()}"

    # Create and run agent
    agent = LogIntelligenceAgentLangGraph()
    result = agent.run(
        question=args.question,
        session_id=session_id,
        persona=args.persona,
        user_id=args.user_id or "cli-user",
    )

    print("--- LangGraph Agent's Final Answer ---")
    print(result["answer"])

    print("\n--- Metadata ---")
    metadata = result["metadata"]
    print(f"Cost: ${metadata.get('total_cost_usd', 0):.4f}")
    print(
        f"Tokens: {metadata.get('total_tokens', 0)} ({metadata.get('total_input_tokens', 0)} input, {metadata.get('total_output_tokens', 0)} output)"
    )
    print(f"API Calls: {metadata.get('api_calls', 0)}")
    print(f"Execution Time: {metadata.get('execution_time_ms', 0):.0f}ms")
